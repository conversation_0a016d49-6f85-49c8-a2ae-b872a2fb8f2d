#!/usr/bin/env python3
"""
Test script to verify frame embedding is working correctly
"""

import sys
import logging
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_frame_path_resolution():
    """Test frame path resolution with different scenarios"""
    logger.info("🧪 Testing frame path resolution...")
    
    try:
        from processors.document_exporter import DocumentExporter
        from config import settings
        
        # Test with session ID
        session_id = 1
        exporter = DocumentExporter(settings.FRAMES_DIR, session_id=session_id)
        
        # Test frame finding logic
        test_filename = "scene_002_frame_00_000132.jpg"
        
        logger.info(f"Looking for frame: {test_filename}")
        logger.info(f"Frames directory: {settings.FRAMES_DIR}")
        logger.info(f"Session ID: {session_id}")
        
        # Check what directories exist
        frames_dir = Path(settings.FRAMES_DIR)
        if frames_dir.exists():
            logger.info(f"Frames directory exists: {frames_dir}")
            subdirs = [d for d in frames_dir.iterdir() if d.is_dir()]
            logger.info(f"Subdirectories: {[d.name for d in subdirs]}")
            
            # Check session directory
            session_dir = frames_dir / str(session_id)
            if session_dir.exists():
                logger.info(f"Session directory exists: {session_dir}")
                frame_files = list(session_dir.glob("*.jpg"))
                logger.info(f"Frame files in session dir: {len(frame_files)}")
                if frame_files:
                    logger.info(f"Sample frame files: {[f.name for f in frame_files[:5]]}")
            else:
                logger.warning(f"Session directory does not exist: {session_dir}")
        else:
            logger.error(f"Frames directory does not exist: {frames_dir}")
        
        # Test the frame finding method
        found_path = exporter._find_frame_file(test_filename)
        if found_path:
            logger.info(f"✅ Frame found: {found_path}")
            
            # Test base64 encoding
            encoded = exporter._encode_image_to_base64(found_path)
            if encoded and encoded.startswith('data:image'):
                logger.info(f"✅ Base64 encoding successful: {len(encoded)} characters")
                return True
            else:
                logger.error("❌ Base64 encoding failed")
                return False
        else:
            logger.error(f"❌ Frame not found: {test_filename}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Frame path resolution test failed: {e}")
        return False

def test_html_export_with_frames():
    """Test HTML export with actual frame embedding"""
    logger.info("🧪 Testing HTML export with frame embedding...")
    
    try:
        from processors.document_exporter import DocumentExporter
        from config import settings
        
        # Create test data with real frame filenames
        session_id = 1
        exporter = DocumentExporter(settings.FRAMES_DIR, session_id=session_id)
        
        # Check what frames actually exist
        frames_dir = Path(settings.FRAMES_DIR) / str(session_id)
        actual_frames = []
        
        if frames_dir.exists():
            frame_files = list(frames_dir.glob("*.jpg"))
            logger.info(f"Found {len(frame_files)} frame files")
            
            # Use actual frame files for test
            for i, frame_file in enumerate(frame_files[:3]):  # Test with first 3 frames
                actual_frames.append({
                    'timestamp': i * 30.0,  # 30 seconds apart
                    'filename': frame_file.name,
                    'frame_type': 'slide',
                    'scene_change_score': 0.8
                })
        
        if not actual_frames:
            logger.warning("No actual frame files found, using dummy data")
            actual_frames = [
                {'timestamp': 10.0, 'filename': 'dummy_frame.jpg', 'frame_type': 'slide', 'scene_change_score': 0.8}
            ]
        
        test_data = {
            'session': {'original_filename': 'test.mp4', 'duration': 300},
            'transcription_segments': [
                {'start_time': 5, 'end_time': 15, 'text': 'Hello everyone', 'confidence': 0.9},
                {'start_time': 20, 'end_time': 30, 'text': 'Let me show you this slide', 'confidence': 0.85}
            ],
            'extracted_frames': actual_frames,
            'summary': {}
        }
        
        logger.info(f"Testing with {len(actual_frames)} frames")
        
        # Generate HTML
        html_content = exporter.export_to_html(test_data)
        
        # Check for embedded images
        base64_images = html_content.count('data:image')
        frame_placeholders = html_content.count('Frame not available')
        
        logger.info(f"HTML export results:")
        logger.info(f"  - Base64 embedded images: {base64_images}")
        logger.info(f"  - Frame placeholders: {frame_placeholders}")
        logger.info(f"  - Total HTML size: {len(html_content)} characters")
        
        if base64_images > 0:
            logger.info("✅ Frame embedding working - images embedded as base64")
            
            # Save test HTML for inspection
            test_html_path = Path("test_frame_embedding.html")
            with open(test_html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            logger.info(f"✅ Test HTML saved to: {test_html_path}")
            
            return True
        elif frame_placeholders > 0:
            logger.warning("⚠️  Frames found but showing as placeholders")
            return False
        else:
            logger.error("❌ No frame content found in HTML")
            return False
            
    except Exception as e:
        logger.error(f"❌ HTML export test failed: {e}")
        return False

def test_synchronized_timeline():
    """Test the synchronized timeline feature"""
    logger.info("🧪 Testing synchronized timeline...")
    
    try:
        from processors.document_exporter import DocumentExporter
        from config import settings
        
        session_id = 1
        exporter = DocumentExporter(settings.FRAMES_DIR, session_id=session_id)
        
        # Test data
        transcription = [
            {'start_time': 5, 'end_time': 15, 'text': 'Hello everyone', 'confidence': 0.9},
            {'start_time': 25, 'end_time': 35, 'text': 'As you can see here', 'confidence': 0.85}
        ]
        
        frames = [
            {'timestamp': 10, 'filename': 'frame_001.jpg', 'frame_type': 'slide'},
            {'timestamp': 30, 'filename': 'frame_002.jpg', 'frame_type': 'speaker'}
        ]
        
        # Test synchronized timeline creation
        timeline = exporter._create_synchronized_timeline(transcription, frames)
        
        logger.info(f"Synchronized timeline created with {len(timeline)} items")
        
        # Check timeline order
        timestamps = [item['timestamp'] for item in timeline]
        expected_order = [5, 10, 25, 30]  # Should be sorted
        
        if timestamps == expected_order:
            logger.info("✅ Timeline properly synchronized and sorted")
            
            # Check types
            types = [item['type'] for item in timeline]
            if 'transcription' in types and 'frame' in types:
                logger.info("✅ Both transcription and frame items in timeline")
                return True
            else:
                logger.error("❌ Missing item types in timeline")
                return False
        else:
            logger.error(f"❌ Timeline not properly sorted: {timestamps} != {expected_order}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Synchronized timeline test failed: {e}")
        return False

def main():
    """Run all frame embedding tests"""
    print("🚀 Testing Frame Embedding Fixes")
    print("=" * 50)
    
    tests = [
        ("Frame Path Resolution", test_frame_path_resolution),
        ("HTML Export with Frames", test_html_export_with_frames),
        ("Synchronized Timeline", test_synchronized_timeline)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Testing {test_name}...")
        if test_func():
            print(f"✅ {test_name}: PASSED")
            passed += 1
        else:
            print(f"❌ {test_name}: FAILED")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All frame embedding tests passed!")
        print("\n📋 Frame embedding is working correctly:")
        print("1. ✅ Frame files are found in session directories")
        print("2. ✅ Base64 encoding works for image embedding")
        print("3. ✅ HTML exports include embedded frame images")
        print("4. ✅ Synchronized timeline combines frames and transcription")
    else:
        print("⚠️  Some tests failed. Check the details above.")
        print("\n💡 Common issues:")
        print("- Frame files not found in expected directories")
        print("- Session directories not created properly")
        print("- Image encoding issues")
    
    print("\n🔧 To test with real data:")
    print("   1. Upload a video file to create session with frames")
    print("   2. Export HTML to see embedded images")
    print("   3. Check test_frame_embedding.html for results")

if __name__ == "__main__":
    main()
