import logging
import json
from pathlib import Path
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session

from config import settings
from database import (
    SessionLocal, Session as DBSession, SessionStatus,
    TranscriptionSegment, ExtractedFrame, TimelineEvent, Summary,
    FrameType, EventType
)
from processors.audio_extractor import AudioExtractor
from processors.transcription_manager import TranscriptionManager
from processors.frame_analyzer import FrameAnalyzer
from processors.timeline_sync import TimelineSynchronizer
from processors.ai_summarizer import AISummarizer

logger = logging.getLogger(__name__)

class VideoProcessor:
    """Main orchestrator for video processing pipeline"""

    def __init__(self):
        self.audio_extractor = AudioExtractor(settings.AUDIO_DIR)

        # Initialize transcription manager with configured service
        transcription_config = {
            'whisper_model': settings.WHISPER_MODEL,
            'whisper_cpp_model_path': settings.WHISPER_CPP_MODEL_PATH,
            'aws_access_key_id': settings.AWS_ACCESS_KEY_ID,
            'aws_secret_access_key': settings.AWS_SECRET_ACCESS_KEY,
            'aws_region': settings.AWS_REGION
        }
        self.transcription_manager = TranscriptionManager(
            service=settings.TRANSCRIPTION_SERVICE,
            **transcription_config
        )

        self.frame_analyzer = FrameAnalyzer(
            settings.FRAMES_DIR,
            settings.SCENE_DETECTION_THRESHOLD,
            settings.BLACK_SCREEN_THRESHOLD
        )
        self.timeline_sync = TimelineSynchronizer()

        # Initialize AI summarizer if API key is available
        if settings.OPENAI_API_KEY:
            self.ai_summarizer = AISummarizer(settings.OPENAI_API_KEY)
        else:
            self.ai_summarizer = None
            logger.warning("OpenAI API key not found. AI summarization will be disabled.")

    def process_video(self, session_id: int, video_path: Path, language: Optional[str] = None) -> bool:
        """
        Complete video processing pipeline

        Args:
            session_id: Database session ID
            video_path: Path to uploaded video file

        Returns:
            True if processing successful, False otherwise
        """
        db = SessionLocal()

        try:
            logger.info(f"Starting video processing for session {session_id}")

            # Update session status
            session = db.query(DBSession).filter(DBSession.id == session_id).first()
            if not session:
                logger.error(f"Session {session_id} not found")
                return False

            # Step 1: Extract audio
            logger.info("Step 1: Extracting audio from video")
            audio_path = self.audio_extractor.extract_audio(video_path, session_id)
            if not audio_path:
                logger.error("Audio extraction failed")
                self._mark_session_failed(db, session_id, "Audio extraction failed")
                return False

            # Get video metadata
            video_info = self.audio_extractor.get_video_info(video_path)
            session.duration = video_info.get('duration', 0)
            db.commit()

            # Step 2: Transcribe audio
            logger.info(f"Step 2: Transcribing audio with {settings.TRANSCRIPTION_SERVICE}")
            if language:
                logger.info(f"Using forced language: {language}")
            transcription_segments, detected_language = self.transcription_manager.transcribe_audio(
                audio_path,
                language=language  # Use provided language or auto-detect
            )

            if not transcription_segments:
                logger.error("Transcription failed")
                self._mark_session_failed(db, session_id, "Transcription failed")
                return False

            # Update session with detected language
            session.language_detected = detected_language
            db.commit()

            # Save transcription segments to database
            self._save_transcription_segments(db, session_id, transcription_segments)

            # Step 3: Analyze video frames
            logger.info("Step 3: Analyzing video frames and detecting scenes")
            scenes = self.frame_analyzer.detect_scenes(video_path)

            if scenes:
                # Extract frames from detected scenes
                extracted_frames = self.frame_analyzer.extract_frames_from_scenes(
                    video_path, scenes, session_id
                )
            else:
                # Fallback to interval-based extraction
                logger.info("Scene detection failed, using interval-based frame extraction")
                extracted_frames = self.frame_analyzer.extract_frames_at_intervals(
                    video_path, session_id, settings.FRAME_EXTRACTION_INTERVAL
                )

            # Save extracted frames to database
            self._save_extracted_frames(db, session_id, extracted_frames)

            # Step 4: Create synchronized timeline
            logger.info("Step 4: Creating synchronized timeline")
            timeline = self.timeline_sync.create_synchronized_timeline(
                session_id,
                transcription_segments,
                extracted_frames,
                scenes,
                session.duration
            )

            # Save timeline events to database
            self._save_timeline_events(db, session_id, timeline.events)

            # Step 5: Generate AI summary (if available)
            if self.ai_summarizer:
                logger.info("Step 5: Generating AI summary")
                try:
                    meeting_summary = self.ai_summarizer.summarize_meeting(timeline)
                    self._save_ai_summary(db, session_id, meeting_summary)
                except Exception as e:
                    logger.error(f"AI summarization failed: {e}")
                    # Continue without AI summary
            else:
                logger.info("Step 5: Skipping AI summary (no API key)")

            # Mark session as completed
            session.status = SessionStatus.COMPLETED
            db.commit()

            logger.info(f"Video processing completed successfully for session {session_id}")
            return True

        except Exception as e:
            logger.error(f"Video processing failed for session {session_id}: {e}")
            self._mark_session_failed(db, session_id, str(e))
            return False

        finally:
            db.close()

    def _save_transcription_segments(
        self,
        db: Session,
        session_id: int,
        segments: list
    ) -> None:
        """Save transcription segments to database"""
        try:
            for segment in segments:
                db_segment = TranscriptionSegment(
                    session_id=session_id,
                    start_time=segment.start_time,
                    end_time=segment.end_time,
                    text=segment.text,
                    confidence=segment.confidence,
                    language=segment.language,
                    speaker_id=getattr(segment, 'speaker_id', None)
                )
                db.add(db_segment)

            db.commit()
            logger.info(f"Saved {len(segments)} transcription segments")

        except Exception as e:
            logger.error(f"Error saving transcription segments: {e}")
            db.rollback()
            raise

    def _save_extracted_frames(
        self,
        db: Session,
        session_id: int,
        frames: list
    ) -> None:
        """Save extracted frames to database"""
        try:
            for frame in frames:
                # Convert frame type string to enum
                frame_type_enum = None
                if frame.frame_type:
                    frame_type_str = frame.frame_type.lower()
                    if frame_type_str == "slide":
                        frame_type_enum = FrameType.SLIDE
                    elif frame_type_str == "speaker":
                        frame_type_enum = FrameType.SPEAKER
                    elif frame_type_str == "transition":
                        frame_type_enum = FrameType.TRANSITION

                db_frame = ExtractedFrame(
                    session_id=session_id,
                    timestamp=frame.timestamp,
                    filename=frame.filename,
                    scene_change_score=frame.scene_change_score,
                    frame_type=frame_type_enum,
                    similarity_to_previous=frame.similarity_to_previous
                )
                db.add(db_frame)

            db.commit()
            logger.info(f"Saved {len(frames)} extracted frames")

        except Exception as e:
            logger.error(f"Error saving extracted frames: {e}")
            db.rollback()
            raise

    def _save_timeline_events(
        self,
        db: Session,
        session_id: int,
        events: list
    ) -> None:
        """Save timeline events to database"""
        try:
            for event in events:
                # Find related transcription segment and frame
                transcription_segment_id = None
                frame_id = None

                if event.transcription_text:
                    # Find matching transcription segment
                    segment = db.query(TranscriptionSegment).filter(
                        TranscriptionSegment.session_id == session_id,
                        TranscriptionSegment.start_time <= event.timestamp,
                        TranscriptionSegment.end_time >= event.timestamp
                    ).first()
                    if segment:
                        transcription_segment_id = segment.id

                if event.frame_filename:
                    # Find matching frame
                    frame = db.query(ExtractedFrame).filter(
                        ExtractedFrame.session_id == session_id,
                        ExtractedFrame.filename == event.frame_filename
                    ).first()
                    if frame:
                        frame_id = frame.id

                # Convert event type string to enum
                event_type_enum = None
                if event.event_type:
                    event_type_str = event.event_type.lower()
                    if event_type_str == "scene_change":
                        event_type_enum = EventType.SCENE_CHANGE
                    elif event_type_str == "speaker_change":
                        event_type_enum = EventType.SPEAKER_CHANGE
                    elif event_type_str == "slide_change":
                        event_type_enum = EventType.SLIDE_CHANGE
                    elif event_type_str == "transcription":
                        event_type_enum = EventType.SCENE_CHANGE  # Map to closest type
                    elif event_type_str == "frame_extract":
                        event_type_enum = EventType.SCENE_CHANGE  # Map to closest type
                    elif event_type_str == "combined":
                        event_type_enum = EventType.SCENE_CHANGE  # Map to closest type

                db_event = TimelineEvent(
                    session_id=session_id,
                    timestamp=event.timestamp,
                    transcription_segment_id=transcription_segment_id,
                    frame_id=frame_id,
                    event_type=event_type_enum or EventType.SCENE_CHANGE
                )
                db.add(db_event)

            db.commit()
            logger.info(f"Saved {len(events)} timeline events")

        except Exception as e:
            logger.error(f"Error saving timeline events: {e}")
            db.rollback()
            raise

    def _save_ai_summary(
        self,
        db: Session,
        session_id: int,
        summary
    ) -> None:
        """Save AI-generated summary to database"""
        try:
            db_summary = Summary(
                session_id=session_id,
                executive_summary=summary.executive_summary,
                key_points=json.dumps(summary.key_points),
                action_items=json.dumps(summary.action_items),
                decisions=json.dumps(summary.decisions),
                slide_descriptions=json.dumps(summary.slide_descriptions),
                topic_timeline=json.dumps(summary.topic_timeline)
            )
            db.add(db_summary)
            db.commit()

            logger.info("AI summary saved successfully")

        except Exception as e:
            logger.error(f"Error saving AI summary: {e}")
            db.rollback()
            raise

    def _mark_session_failed(self, db: Session, session_id: int, error_message: str) -> None:
        """Mark session as failed with error message"""
        try:
            session = db.query(DBSession).filter(DBSession.id == session_id).first()
            if session:
                session.status = SessionStatus.FAILED
                db.commit()

            logger.error(f"Session {session_id} marked as failed: {error_message}")

        except Exception as e:
            logger.error(f"Error marking session as failed: {e}")

    def cleanup_session_files(self, session_id: int) -> None:
        """Clean up temporary files for a session"""
        try:
            self.audio_extractor.cleanup_session_audio(session_id)
            self.frame_analyzer.cleanup_session_frames(session_id)
            logger.info(f"Cleaned up files for session {session_id}")
        except Exception as e:
            logger.error(f"Error cleaning up session files: {e}")

    def get_processing_status(self, session_id: int) -> Dict[str, Any]:
        """Get detailed processing status for a session"""
        db = SessionLocal()
        try:
            session = db.query(DBSession).filter(DBSession.id == session_id).first()
            if not session:
                return {"error": "Session not found"}

            # Count processed items
            transcription_count = db.query(TranscriptionSegment).filter(
                TranscriptionSegment.session_id == session_id
            ).count()

            frame_count = db.query(ExtractedFrame).filter(
                ExtractedFrame.session_id == session_id
            ).count()

            timeline_count = db.query(TimelineEvent).filter(
                TimelineEvent.session_id == session_id
            ).count()

            summary_exists = db.query(Summary).filter(
                Summary.session_id == session_id
            ).first() is not None

            return {
                "session_id": session_id,
                "status": session.status.value,
                "duration": session.duration,
                "language_detected": session.language_detected,
                "processing_stats": {
                    "transcription_segments": transcription_count,
                    "extracted_frames": frame_count,
                    "timeline_events": timeline_count,
                    "ai_summary_available": summary_exists
                },
                "created_at": session.created_at,
                "completed_at": session.completed_at
            }

        except Exception as e:
            logger.error(f"Error getting processing status: {e}")
            return {"error": str(e)}
        finally:
            db.close()

# Global processor instance
video_processor = VideoProcessor()