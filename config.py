import os
from dotenv import load_dotenv
from pathlib import Path

load_dotenv()

class Settings:
    # API Configuration
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")

    # AWS Configuration
    AWS_ACCESS_KEY_ID: str = os.getenv("AWS_ACCESS_KEY_ID", "")
    AWS_SECRET_ACCESS_KEY: str = os.getenv("AWS_SECRET_ACCESS_KEY", "")
    AWS_REGION: str = os.getenv("AWS_REGION", "us-east-1")

    # Database Configuration
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./meeting_analysis.db")

    # Redis Configuration
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")

    # File Upload Settings
    MAX_FILE_SIZE_MB: int = int(os.getenv("MAX_FILE_SIZE_MB", "500"))
    ALLOWED_VIDEO_EXTENSIONS: list = os.getenv("ALLOWED_VIDEO_EXTENSIONS", "mp4,mov,avi,mkv").split(",")

    # Transcription Settings
    TRANSCRIPTION_SERVICE: str = os.getenv("TRANSCRIPTION_SERVICE", "whisper")  # whisper, whisper_cpp, aws_transcribe
    WHISPER_MODEL: str = os.getenv("WHISPER_MODEL", "large-v3")
    WHISPER_CPP_MODEL_PATH: str = os.getenv("WHISPER_CPP_MODEL_PATH", "")

    # Frame Analysis Settings
    SCENE_DETECTION_THRESHOLD: float = float(os.getenv("SCENE_DETECTION_THRESHOLD", "27.0"))
    FRAME_EXTRACTION_INTERVAL: int = int(os.getenv("FRAME_EXTRACTION_INTERVAL", "10"))
    BLACK_SCREEN_THRESHOLD: float = float(os.getenv("BLACK_SCREEN_THRESHOLD", "15.0"))  # Mean intensity threshold for black screens

    # Directory Paths
    UPLOAD_DIR: Path = Path("uploads")
    FRAMES_DIR: Path = Path("frames")
    AUDIO_DIR: Path = Path("audio")

    # Supported Languages
    SUPPORTED_LANGUAGES: list = ["en", "ru", "uk"]

settings = Settings()