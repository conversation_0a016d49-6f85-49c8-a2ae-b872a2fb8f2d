import subprocess
import json
import logging
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import tempfile
import os

from .transcription import TranscriptionSegment

logger = logging.getLogger(__name__)

class WhisperCppTranscriber:
    """Whisper.cpp transcriber for faster CPU inference"""
    
    def __init__(self, model_path: str):
        self.model_path = Path(model_path)
        self.supported_languages = ["en", "ru", "uk"]
        
        # Language mapping for whisper.cpp
        self.language_mapping = {
            "en": "en",
            "ru": "ru",
            "uk": "uk"
        }
        
        # Verify model exists
        if not self.model_path.exists():
            raise FileNotFoundError(f"Whisper.cpp model not found: {model_path}")
        
        # Try to find whisper.cpp executable
        self.whisper_cpp_path = self._find_whisper_cpp()
        
        logger.info(f"Whisper.cpp initialized with model: {self.model_path}")
    
    def _find_whisper_cpp(self) -> str:
        """Find whisper.cpp executable"""
        # Common locations for whisper.cpp
        possible_paths = [
            "whisper",
            "./whisper",
            "/usr/local/bin/whisper",
            "/opt/homebrew/bin/whisper",
            "whisper.cpp/main",
            "./whisper.cpp/main"
        ]
        
        for path in possible_paths:
            try:
                result = subprocess.run([path, "--help"], 
                                      capture_output=True, 
                                      text=True, 
                                      timeout=5)
                if result.returncode == 0:
                    logger.info(f"Found whisper.cpp at: {path}")
                    return path
            except (subprocess.TimeoutExpired, FileNotFoundError):
                continue
        
        # If not found, assume it's in PATH
        logger.warning("whisper.cpp executable not found in common locations, assuming it's in PATH")
        return "whisper"
    
    def transcribe_audio(
        self, 
        audio_path: Path,
        language: Optional[str] = None,
        **kwargs
    ) -> Tuple[List[TranscriptionSegment], str]:
        """
        Transcribe audio using whisper.cpp
        
        Args:
            audio_path: Path to audio file
            language: Language code ("en", "ru", "uk") or None for auto-detect
            
        Returns:
            Tuple of (transcription_segments, detected_language)
        """
        try:
            logger.info(f"Starting whisper.cpp transcription for {audio_path}")
            
            # Detect language if not provided
            if not language:
                language = self.detect_language(audio_path)
                logger.info(f"Auto-detected language: {language}")
            
            # Map language to whisper.cpp format
            whisper_language = self.language_mapping.get(language, "en")
            
            # Create temporary output file for JSON results
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
                temp_output_path = temp_file.name
            
            try:
                # Build whisper.cpp command
                cmd = [
                    self.whisper_cpp_path,
                    "-m", str(self.model_path),
                    "-f", str(audio_path),
                    "-l", whisper_language,
                    "-oj",  # Output JSON
                    "-of", temp_output_path.replace('.json', ''),  # Output file prefix
                    "-t", "4",  # Use 4 threads
                    "-ml", "1"  # Max line length
                ]
                
                # Add word-level timestamps if supported
                cmd.extend(["-ml", "1", "-sow"])  # Single word output
                
                logger.info(f"Running whisper.cpp command: {' '.join(cmd)}")
                
                # Run whisper.cpp
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=3600  # 1 hour timeout
                )
                
                if result.returncode != 0:
                    logger.error(f"whisper.cpp failed: {result.stderr}")
                    return [], "unknown"
                
                # Process results
                segments = self._process_whisper_cpp_output(temp_output_path, language)
                
                logger.info(f"whisper.cpp completed: {len(segments)} segments")
                return segments, language
                
            finally:
                # Cleanup temporary files
                if os.path.exists(temp_output_path):
                    os.unlink(temp_output_path)
                
                # whisper.cpp might create additional files
                base_name = temp_output_path.replace('.json', '')
                for ext in ['.json', '.txt', '.srt', '.vtt']:
                    cleanup_file = base_name + ext
                    if os.path.exists(cleanup_file):
                        os.unlink(cleanup_file)
            
        except Exception as e:
            logger.error(f"whisper.cpp transcription failed: {e}")
            return [], "unknown"
    
    def _process_whisper_cpp_output(self, output_path: str, language: str) -> List[TranscriptionSegment]:
        """Process whisper.cpp JSON output into our format"""
        try:
            # whisper.cpp creates files with different extensions
            json_file = output_path.replace('.json', '.json')
            if not os.path.exists(json_file):
                # Try alternative naming
                json_file = output_path.replace('.json', '') + '.json'
            
            if not os.path.exists(json_file):
                logger.error(f"whisper.cpp output file not found: {json_file}")
                return []
            
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            segments = []
            
            # Process transcription data
            if 'transcription' in data:
                transcription_data = data['transcription']
                
                for segment_data in transcription_data:
                    # Extract segment information
                    start_time = segment_data.get('offsets', {}).get('from', 0) / 1000.0  # Convert ms to seconds
                    end_time = segment_data.get('offsets', {}).get('to', 0) / 1000.0
                    text = segment_data.get('text', '').strip()
                    
                    if not text:
                        continue
                    
                    # Extract word-level information if available
                    words = []
                    if 'words' in segment_data:
                        for word_data in segment_data['words']:
                            word_info = {
                                'word': word_data.get('word', '').strip(),
                                'start': word_data.get('offsets', {}).get('from', 0) / 1000.0,
                                'end': word_data.get('offsets', {}).get('to', 0) / 1000.0,
                                'confidence': word_data.get('confidence', 1.0)
                            }
                            words.append(word_info)
                    
                    # Calculate confidence (whisper.cpp might not provide this)
                    confidence = segment_data.get('confidence', 1.0)
                    if confidence is None and words:
                        # Calculate from word confidences
                        word_confidences = [w.get('confidence', 1.0) for w in words]
                        confidence = sum(word_confidences) / len(word_confidences)
                    elif confidence is None:
                        confidence = 1.0
                    
                    segment = TranscriptionSegment(
                        start_time=start_time,
                        end_time=end_time,
                        text=text,
                        confidence=confidence,
                        language=language,
                        words=words
                    )
                    
                    segments.append(segment)
            
            return segments
            
        except Exception as e:
            logger.error(f"Failed to process whisper.cpp output: {e}")
            return []
    
    def detect_language(self, audio_path: Path) -> str:
        """
        Detect language using whisper.cpp
        """
        try:
            # Run whisper.cpp with language detection
            cmd = [
                self.whisper_cpp_path,
                "-m", str(self.model_path),
                "-f", str(audio_path),
                "-t", "2",  # Use 2 threads for faster detection
                "-d"  # Detect language only
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=60  # 1 minute timeout for detection
            )
            
            if result.returncode == 0:
                # Parse language from output
                output = result.stdout.lower()
                
                # Look for language indicators in output
                if 'russian' in output or 'ru' in output:
                    return 'ru'
                elif 'ukrainian' in output or 'uk' in output:
                    return 'uk'
                elif 'english' in output or 'en' in output:
                    return 'en'
            
            logger.warning("Language detection failed, defaulting to English")
            return "en"
            
        except Exception as e:
            logger.error(f"Language detection failed: {e}")
            return "en"
