import subprocess
import json
import logging
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import tempfile
import os

from .transcription import TranscriptionSegment

logger = logging.getLogger(__name__)

class WhisperCppTranscriber:
    """Whisper.cpp transcriber for faster CPU inference"""

    def __init__(self, model_path: str):
        self.model_path = Path(model_path)
        self.supported_languages = ["en", "ru", "uk"]

        # Language mapping for whisper.cpp
        self.language_mapping = {
            "en": "en",
            "ru": "ru",
            "uk": "uk"
        }

        # Verify model exists
        if not self.model_path.exists():
            raise FileNotFoundError(f"Whisper.cpp model not found: {model_path}")

        # Try to find whisper.cpp executable
        self.whisper_cpp_path = self._find_whisper_cpp()

        logger.info(f"Whisper.cpp initialized with model: {self.model_path}")

    def _find_whisper_cpp(self) -> str:
        """Find whisper.cpp executable"""
        # Common locations for whisper.cpp
        possible_paths = [
            "whisper",
            "./whisper",
            "/usr/local/bin/whisper",
            "/opt/homebrew/bin/whisper",
            "whisper.cpp/main",
            "./whisper.cpp/main"
        ]

        for path in possible_paths:
            try:
                result = subprocess.run([path, "--help"],
                                      capture_output=True,
                                      text=True,
                                      timeout=5)
                if result.returncode == 0:
                    logger.info(f"Found whisper.cpp at: {path}")
                    return path
            except (subprocess.TimeoutExpired, FileNotFoundError):
                continue

        # If not found, assume it's in PATH
        logger.warning("whisper.cpp executable not found in common locations, assuming it's in PATH")
        return "whisper"

    def transcribe_audio(
        self,
        audio_path: Path,
        language: Optional[str] = None,
        **kwargs
    ) -> Tuple[List[TranscriptionSegment], str]:
        """
        Transcribe audio using whisper.cpp

        Args:
            audio_path: Path to audio file
            language: Language code ("en", "ru", "uk") or None for auto-detect

        Returns:
            Tuple of (transcription_segments, detected_language)
        """
        try:
            logger.info(f"Starting whisper.cpp transcription for {audio_path}")

            # Detect language if not provided
            if not language:
                language = self.detect_language(audio_path)
                logger.info(f"Auto-detected language: {language}")

            # Map language to whisper.cpp format
            whisper_language = self.language_mapping.get(language, "en")

            # Create temporary output file for JSON results
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
                temp_output_path = temp_file.name

            try:
                # Build whisper.cpp command for better segmentation
                cmd = [
                    self.whisper_cpp_path,
                    "-m", str(self.model_path),
                    "-f", str(audio_path),
                    "-l", whisper_language,
                    "-osrt",  # Output SRT format (better for segmentation)
                    "-of", temp_output_path.replace('.json', ''),  # Output file prefix
                    "-t", "4",  # Use 4 threads
                    "-ml", "0",  # No max line length limit
                    "-su",  # Speed up 2x
                    "-tr",  # Translate to English (if needed)
                    "-pp",  # Print progress
                    "-nt"   # No timestamps in text output
                ]

                # Remove translate flag if language is already English
                if whisper_language == "en":
                    cmd.remove("-tr")

                # Add word-level timestamps for better segmentation
                cmd.extend(["-sow", "-wts"])  # Single word output with word timestamps

                logger.info(f"Running whisper.cpp command: {' '.join(cmd)}")

                # Run whisper.cpp
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=3600  # 1 hour timeout
                )

                if result.returncode != 0:
                    logger.error(f"whisper.cpp failed: {result.stderr}")
                    return [], "unknown"

                # Process results
                segments = self._process_whisper_cpp_output(temp_output_path, language)

                logger.info(f"whisper.cpp completed: {len(segments)} segments")
                return segments, language

            finally:
                # Cleanup temporary files
                if os.path.exists(temp_output_path):
                    os.unlink(temp_output_path)

                # whisper.cpp might create additional files
                base_name = temp_output_path.replace('.json', '')
                for ext in ['.json', '.txt', '.srt', '.vtt']:
                    cleanup_file = base_name + ext
                    if os.path.exists(cleanup_file):
                        os.unlink(cleanup_file)

        except Exception as e:
            logger.error(f"whisper.cpp transcription failed: {e}")
            return [], "unknown"

    def _process_whisper_cpp_output(self, output_path: str, language: str) -> List[TranscriptionSegment]:
        """Process whisper.cpp output into our format"""
        try:
            # whisper.cpp can create different output files
            # Try to find the actual output file
            base_path = output_path.replace('.json', '')
            possible_files = [
                f"{base_path}.json",
                f"{base_path}.txt",
                f"{base_path}.srt",
                f"{base_path}.vtt"
            ]

            output_file = None
            for file_path in possible_files:
                if os.path.exists(file_path):
                    output_file = file_path
                    break

            if not output_file:
                logger.error(f"No whisper.cpp output file found. Tried: {possible_files}")
                return []

            logger.info(f"Processing whisper.cpp output from: {output_file}")

            # Process based on file type
            if output_file.endswith('.json'):
                return self._process_json_output(output_file, language)
            elif output_file.endswith('.srt'):
                return self._process_srt_output(output_file, language)
            elif output_file.endswith('.vtt'):
                return self._process_vtt_output(output_file, language)
            elif output_file.endswith('.txt'):
                return self._process_txt_output(output_file, language)
            else:
                logger.error(f"Unsupported output file format: {output_file}")
                return []

        except Exception as e:
            logger.error(f"Failed to process whisper.cpp output: {e}")
            return []

    def _process_json_output(self, json_file: str, language: str) -> List[TranscriptionSegment]:
        """Process JSON output from whisper.cpp"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            segments = []

            # Handle different JSON formats from whisper.cpp
            if 'transcription' in data:
                # Format 1: {transcription: [...]}
                transcription_data = data['transcription']
            elif isinstance(data, list):
                # Format 2: [...]
                transcription_data = data
            elif 'segments' in data:
                # Format 3: {segments: [...]}
                transcription_data = data['segments']
            else:
                logger.warning(f"Unknown JSON format in {json_file}")
                return []

            for segment_data in transcription_data:
                # Extract segment information with flexible field names
                start_time = self._extract_time(segment_data, ['start', 'from', 'start_time'], 0.0)
                end_time = self._extract_time(segment_data, ['end', 'to', 'end_time'], 0.0)
                text = segment_data.get('text', '').strip()

                if not text:
                    continue

                # Extract word-level information if available
                words = []
                if 'words' in segment_data:
                    for word_data in segment_data['words']:
                        word_info = {
                            'word': word_data.get('word', word_data.get('text', '')).strip(),
                            'start': self._extract_time(word_data, ['start', 'from'], 0.0),
                            'end': self._extract_time(word_data, ['end', 'to'], 0.0),
                            'confidence': word_data.get('confidence', 1.0)
                        }
                        words.append(word_info)

                # Calculate confidence
                confidence = segment_data.get('confidence', 1.0)
                if confidence is None and words:
                    word_confidences = [w.get('confidence', 1.0) for w in words]
                    confidence = sum(word_confidences) / len(word_confidences)
                elif confidence is None:
                    confidence = 1.0

                segment = TranscriptionSegment(
                    start_time=start_time,
                    end_time=end_time,
                    text=text,
                    confidence=confidence,
                    language=language,
                    words=words
                )

                segments.append(segment)

            return segments

        except Exception as e:
            logger.error(f"Failed to process JSON output: {e}")
            return []

    def _process_srt_output(self, srt_file: str, language: str) -> List[TranscriptionSegment]:
        """Process SRT output from whisper.cpp"""
        try:
            segments = []

            with open(srt_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # Parse SRT format
            srt_blocks = content.strip().split('\n\n')

            for block in srt_blocks:
                lines = block.strip().split('\n')
                if len(lines) < 3:
                    continue

                # Parse timestamp line (format: 00:00:00,000 --> 00:00:05,000)
                timestamp_line = lines[1]
                if ' --> ' not in timestamp_line:
                    continue

                start_str, end_str = timestamp_line.split(' --> ')
                start_time = self._parse_srt_time(start_str)
                end_time = self._parse_srt_time(end_str)

                # Get text (everything after timestamp line)
                text = ' '.join(lines[2:]).strip()

                if text:
                    segment = TranscriptionSegment(
                        start_time=start_time,
                        end_time=end_time,
                        text=text,
                        confidence=1.0,  # SRT doesn't include confidence
                        language=language,
                        words=[]  # SRT doesn't include word-level timing
                    )
                    segments.append(segment)

            return segments

        except Exception as e:
            logger.error(f"Failed to process SRT output: {e}")
            return []

    def _process_vtt_output(self, vtt_file: str, language: str) -> List[TranscriptionSegment]:
        """Process VTT output from whisper.cpp"""
        try:
            segments = []

            with open(vtt_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Skip WEBVTT header
            start_idx = 0
            for i, line in enumerate(lines):
                if line.strip() == 'WEBVTT':
                    start_idx = i + 1
                    break

            # Parse VTT blocks
            current_block = []
            for line in lines[start_idx:]:
                line = line.strip()
                if not line:
                    if current_block:
                        segment = self._parse_vtt_block(current_block, language)
                        if segment:
                            segments.append(segment)
                        current_block = []
                else:
                    current_block.append(line)

            # Process last block
            if current_block:
                segment = self._parse_vtt_block(current_block, language)
                if segment:
                    segments.append(segment)

            return segments

        except Exception as e:
            logger.error(f"Failed to process VTT output: {e}")
            return []

    def _process_txt_output(self, txt_file: str, language: str) -> List[TranscriptionSegment]:
        """Process plain text output from whisper.cpp"""
        try:
            with open(txt_file, 'r', encoding='utf-8') as f:
                text = f.read().strip()

            if not text:
                return []

            # For plain text, create a single segment
            # This is not ideal but better than nothing
            segment = TranscriptionSegment(
                start_time=0.0,
                end_time=0.0,  # Unknown duration
                text=text,
                confidence=1.0,
                language=language,
                words=[]
            )

            return [segment]

        except Exception as e:
            logger.error(f"Failed to process TXT output: {e}")
            return []

    def _extract_time(self, data: dict, field_names: list, default: float = 0.0) -> float:
        """Extract time value from data with flexible field names"""
        for field in field_names:
            if field in data:
                value = data[field]
                if isinstance(value, dict) and 'from' in value:
                    # Handle nested time objects
                    return value['from'] / 1000.0
                elif isinstance(value, (int, float)):
                    # Handle direct time values (might be in ms or seconds)
                    if value > 10000:  # Likely milliseconds
                        return value / 1000.0
                    else:  # Likely seconds
                        return float(value)
        return default

    def _parse_srt_time(self, time_str: str) -> float:
        """Parse SRT time format (HH:MM:SS,mmm) to seconds"""
        try:
            time_part, ms_part = time_str.split(',')
            h, m, s = map(int, time_part.split(':'))
            ms = int(ms_part)
            return h * 3600 + m * 60 + s + ms / 1000.0
        except:
            return 0.0

    def _parse_vtt_block(self, block_lines: list, language: str) -> Optional[TranscriptionSegment]:
        """Parse a VTT block into a transcription segment"""
        try:
            if len(block_lines) < 2:
                return None

            # First line should be timestamp
            timestamp_line = block_lines[0]
            if ' --> ' not in timestamp_line:
                return None

            start_str, end_str = timestamp_line.split(' --> ')
            start_time = self._parse_vtt_time(start_str)
            end_time = self._parse_vtt_time(end_str)

            # Rest is text
            text = ' '.join(block_lines[1:]).strip()

            if text:
                return TranscriptionSegment(
                    start_time=start_time,
                    end_time=end_time,
                    text=text,
                    confidence=1.0,
                    language=language,
                    words=[]
                )

            return None

        except Exception as e:
            logger.error(f"Failed to parse VTT block: {e}")
            return None

    def _parse_vtt_time(self, time_str: str) -> float:
        """Parse VTT time format (HH:MM:SS.mmm) to seconds"""
        try:
            if '.' in time_str:
                time_part, ms_part = time_str.split('.')
                ms = int(ms_part)
            else:
                time_part = time_str
                ms = 0

            parts = time_part.split(':')
            if len(parts) == 3:
                h, m, s = map(int, parts)
                return h * 3600 + m * 60 + s + ms / 1000.0
            elif len(parts) == 2:
                m, s = map(int, parts)
                return m * 60 + s + ms / 1000.0
            else:
                return float(parts[0]) + ms / 1000.0
        except:
            return 0.0

    def detect_language(self, audio_path: Path) -> str:
        """
        Detect language using whisper.cpp
        """
        try:
            # Run whisper.cpp with language detection
            cmd = [
                self.whisper_cpp_path,
                "-m", str(self.model_path),
                "-f", str(audio_path),
                "-t", "2",  # Use 2 threads for faster detection
                "-d"  # Detect language only
            ]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=60  # 1 minute timeout for detection
            )

            if result.returncode == 0:
                # Parse language from output
                output = result.stdout.lower()

                # Look for language indicators in output
                if 'russian' in output or 'ru' in output:
                    return 'ru'
                elif 'ukrainian' in output or 'uk' in output:
                    return 'uk'
                elif 'english' in output or 'en' in output:
                    return 'en'

            logger.warning("Language detection failed, defaulting to English")
            return "en"

        except Exception as e:
            logger.error(f"Language detection failed: {e}")
            return "en"
