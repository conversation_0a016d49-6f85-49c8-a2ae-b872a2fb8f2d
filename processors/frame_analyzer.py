import cv2
import numpy as np
import ffmpeg
from scenedetect import open_video, SceneManager
from scenedetect.detectors import ContentDetector, AdaptiveDetector, ThresholdDetector
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
import logging
from skimage.metrics import structural_similarity as ssim

logger = logging.getLogger(__name__)

@dataclass
class SceneInfo:
    """Data class for scene information"""
    start_time: float
    end_time: float
    duration: float
    scene_type: str
    confidence: float

@dataclass
class ExtractedFrame:
    """Data class for extracted frame information"""
    timestamp: float
    filename: str
    scene_change_score: float
    frame_type: str
    similarity_to_previous: float
    width: int
    height: int

class FrameAnalyzer:
    """Advanced frame extraction and scene analysis"""

    def __init__(self, output_dir: Path, content_threshold: float = 27.0, black_screen_threshold: float = 15.0):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.content_threshold = content_threshold
        self.black_screen_threshold = black_screen_threshold

    def detect_scenes(self, video_path: Path) -> List[SceneInfo]:
        """
        Detect scene changes in video using multiple detectors

        Args:
            video_path: Path to video file

        Returns:
            List of scene information
        """
        try:
            logger.info(f"Detecting scenes in {video_path}")

            # Open video
            video = open_video(str(video_path))
            scene_manager = SceneManager()

            # Add multiple detectors for robust scene detection
            scene_manager.add_detector(ContentDetector(threshold=self.content_threshold))
            scene_manager.add_detector(AdaptiveDetector())
            scene_manager.add_detector(ThresholdDetector(threshold=12.0))

            # Detect scenes
            scene_manager.detect_scenes(video, show_progress=False)
            scene_list = scene_manager.get_scene_list()

            # Process scene list
            scenes = []
            for i, (start_time, end_time) in enumerate(scene_list):
                scene = SceneInfo(
                    start_time=start_time.get_seconds(),
                    end_time=end_time.get_seconds(),
                    duration=(end_time - start_time).get_seconds(),
                    scene_type=self._classify_scene_type(i, len(scene_list)),
                    confidence=0.8  # Default confidence
                )
                scenes.append(scene)

            logger.info(f"Detected {len(scenes)} scenes")
            return scenes

        except Exception as e:
            logger.error(f"Scene detection failed: {e}")
            return []

    def extract_frames_from_scenes(
        self,
        video_path: Path,
        scenes: List[SceneInfo],
        session_id: int,
        max_frames_per_scene: int = 3
    ) -> List[ExtractedFrame]:
        """
        Extract representative frames from detected scenes

        Args:
            video_path: Path to video file
            scenes: List of detected scenes
            session_id: Session ID for organizing files
            max_frames_per_scene: Maximum frames to extract per scene

        Returns:
            List of extracted frame information
        """
        extracted_frames = []

        try:
            # Create session directory
            session_dir = self.output_dir / str(session_id)
            session_dir.mkdir(exist_ok=True)

            # Open video for frame extraction
            cap = cv2.VideoCapture(str(video_path))
            fps = cap.get(cv2.CAP_PROP_FPS)

            previous_frame = None
            frame_count = 0

            for scene_idx, scene in enumerate(scenes):
                # Calculate frame extraction points within the scene
                scene_duration = scene.end_time - scene.start_time

                if scene_duration < 1.0:  # Very short scene, extract middle frame only
                    extraction_points = [scene.start_time + scene_duration / 2]
                else:
                    # Extract frames at strategic points in the scene
                    num_frames = min(max_frames_per_scene, max(1, int(scene_duration / 5)))
                    extraction_points = []

                    for i in range(num_frames):
                        # Distribute frames evenly across the scene
                        point = scene.start_time + (i + 1) * scene_duration / (num_frames + 1)
                        extraction_points.append(point)

                # Extract frames at calculated points
                for point_idx, timestamp in enumerate(extraction_points):
                    frame_info = self._extract_frame_at_timestamp(
                        cap, timestamp, fps, session_dir,
                        f"scene_{scene_idx:03d}_frame_{point_idx:02d}",
                        previous_frame, scene.scene_type, self.black_screen_threshold
                    )

                    if frame_info:
                        extracted_frames.append(frame_info)
                        if frame_info.filename:
                            # Load frame for next comparison
                            frame_path = session_dir / frame_info.filename
                            if frame_path.exists():
                                previous_frame = cv2.imread(str(frame_path))
                        frame_count += 1

            cap.release()
            logger.info(f"Extracted {frame_count} frames from {len(scenes)} scenes")
            return extracted_frames

        except Exception as e:
            logger.error(f"Frame extraction failed: {e}")
            return []

    def _extract_frame_at_timestamp(
        self,
        cap: cv2.VideoCapture,
        timestamp: float,
        fps: float,
        output_dir: Path,
        base_filename: str,
        previous_frame: Optional[np.ndarray],
        scene_type: str,
        black_screen_threshold: float = 15.0
    ) -> Optional[ExtractedFrame]:
        """Extract a single frame at specified timestamp"""
        try:
            # Calculate frame number
            frame_number = int(timestamp * fps)

            # Seek to frame
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            ret, frame = cap.read()

            if not ret:
                return None

            # Check if frame is a black screen
            if self._is_black_screen(frame, black_screen_threshold):
                logger.debug(f"Skipping black screen at {timestamp}s")
                return None

            # Generate filename
            filename = f"{base_filename}_{int(timestamp):06d}.jpg"
            frame_path = output_dir / filename

            # Save frame with high quality
            cv2.imwrite(str(frame_path), frame, [cv2.IMWRITE_JPEG_QUALITY, 95])

            # Calculate similarity to previous frame
            similarity_score = 0.0
            if previous_frame is not None:
                similarity_score = self._calculate_frame_similarity(previous_frame, frame)

            # Calculate scene change score (simplified)
            scene_change_score = 1.0 - similarity_score if similarity_score > 0 else 1.0

            return ExtractedFrame(
                timestamp=timestamp,
                filename=filename,
                scene_change_score=scene_change_score,
                frame_type=self._classify_frame_type(frame, scene_type),
                similarity_to_previous=similarity_score,
                width=frame.shape[1],
                height=frame.shape[0]
            )

        except Exception as e:
            logger.error(f"Error extracting frame at {timestamp}s: {e}")
            return None

    def extract_frames_at_intervals(
        self,
        video_path: Path,
        session_id: int,
        interval_seconds: int = 10
    ) -> List[ExtractedFrame]:
        """
        Extract frames at regular intervals (fallback method)

        Args:
            video_path: Path to video file
            session_id: Session ID for organizing files
            interval_seconds: Interval between frames in seconds

        Returns:
            List of extracted frame information
        """
        extracted_frames = []

        try:
            # Create session directory
            session_dir = self.output_dir / str(session_id)
            session_dir.mkdir(exist_ok=True)

            # Get video info
            probe = ffmpeg.probe(str(video_path))
            duration = float(probe['format']['duration'])

            # Calculate extraction points
            timestamps = []
            current_time = 0
            while current_time < duration:
                timestamps.append(current_time)
                current_time += interval_seconds

            # Add final frame
            if duration - timestamps[-1] > interval_seconds / 2:
                timestamps.append(duration - 1)

            # Extract frames using FFmpeg for better performance
            for i, timestamp in enumerate(timestamps):
                filename = f"interval_{i:04d}_{int(timestamp):06d}.jpg"
                frame_path = session_dir / filename

                try:
                    # Use FFmpeg for high-quality frame extraction
                    (
                        ffmpeg
                        .input(str(video_path), ss=timestamp)
                        .output(str(frame_path), vframes=1, q=2)
                        .overwrite_output()
                        .run(quiet=True)
                    )

                    if frame_path.exists():
                        # Check if extracted frame is a black screen
                        frame = cv2.imread(str(frame_path))
                        if frame is not None and self._is_black_screen(frame, self.black_screen_threshold):
                            logger.debug(f"Skipping black screen at {timestamp}s")
                            frame_path.unlink()  # Delete the black screen image
                            continue

                        # Calculate similarity if previous frame exists
                        similarity_score = 0.0
                        if i > 0:
                            prev_frame_path = session_dir / f"interval_{i-1:04d}_{int(timestamps[i-1]):06d}.jpg"
                            if prev_frame_path.exists():
                                similarity_score = self._calculate_image_similarity(prev_frame_path, frame_path)

                        extracted_frames.append(ExtractedFrame(
                            timestamp=timestamp,
                            filename=filename,
                            scene_change_score=1.0 - similarity_score,
                            frame_type="interval",
                            similarity_to_previous=similarity_score,
                            width=frame.shape[1] if frame is not None else 0,
                            height=frame.shape[0] if frame is not None else 0
                        ))

                except Exception as e:
                    logger.warning(f"Failed to extract frame at {timestamp}s: {e}")
                    continue

            logger.info(f"Extracted {len(extracted_frames)} frames at {interval_seconds}s intervals")
            return extracted_frames

        except Exception as e:
            logger.error(f"Interval frame extraction failed: {e}")
            return []

    def _calculate_frame_similarity(self, frame1: np.ndarray, frame2: np.ndarray) -> float:
        """Calculate similarity between two frames using SSIM"""
        try:
            # Convert to grayscale
            gray1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY)
            gray2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY)

            # Resize to same dimensions if needed
            if gray1.shape != gray2.shape:
                height, width = min(gray1.shape[0], gray2.shape[0]), min(gray1.shape[1], gray2.shape[1])
                gray1 = cv2.resize(gray1, (width, height))
                gray2 = cv2.resize(gray2, (width, height))

            # Calculate SSIM
            similarity = ssim(gray1, gray2)
            return max(0.0, similarity)

        except Exception as e:
            logger.error(f"Error calculating frame similarity: {e}")
            return 0.0

    def _calculate_image_similarity(self, image_path1: Path, image_path2: Path) -> float:
        """Calculate similarity between two image files"""
        try:
            img1 = cv2.imread(str(image_path1))
            img2 = cv2.imread(str(image_path2))

            if img1 is None or img2 is None:
                return 0.0

            return self._calculate_frame_similarity(img1, img2)

        except Exception as e:
            logger.error(f"Error calculating image similarity: {e}")
            return 0.0

    def _classify_scene_type(self, scene_index: int, total_scenes: int) -> str:
        """Simple scene type classification"""
        if scene_index == 0:
            return "opening"
        elif scene_index == total_scenes - 1:
            return "closing"
        else:
            return "content"

    def _is_black_screen(self, frame: np.ndarray, threshold: float = 15.0) -> bool:
        """
        Detect if frame is a black screen

        Args:
            frame: Input frame
            threshold: Mean intensity threshold for black screen detection

        Returns:
            True if frame is considered a black screen
        """
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            # Calculate mean intensity
            mean_intensity = np.mean(gray)

            # Check if mean intensity is below threshold
            is_black = mean_intensity < threshold

            if is_black:
                logger.debug(f"Black screen detected: mean intensity {mean_intensity:.2f} < {threshold}")

            return is_black

        except Exception as e:
            logger.error(f"Error detecting black screen: {e}")
            return False

    def _classify_frame_type(self, frame: np.ndarray, scene_type: str) -> str:
        """
        Enhanced frame type classification based on visual characteristics
        """
        try:
            # Convert to grayscale for analysis
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            # Calculate basic statistics
            mean_intensity = np.mean(gray)
            std_intensity = np.std(gray)

            # Calculate edge density for content detection
            edges = cv2.Canny(gray, 50, 150)
            edge_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])

            # Enhanced heuristics for frame classification
            if mean_intensity < 20:  # Very dark frame
                return "transition"
            elif std_intensity < 25 and edge_density < 0.05:  # Low variance and few edges - likely slide
                return "slide"
            elif std_intensity < 40 and mean_intensity > 200:  # Low variance, high brightness - text slide
                return "slide"
            elif edge_density > 0.15:  # High edge density - complex content or speaker
                return "speaker"
            elif std_intensity > 60:  # High variance - likely speaker or complex scene
                return "speaker"
            else:
                return "content"  # General content

        except Exception as e:
            logger.error(f"Error classifying frame type: {e}")
            return "unknown"

    def cleanup_session_frames(self, session_id: int) -> None:
        """Clean up frame files for a session"""
        session_dir = self.output_dir / str(session_id)
        if session_dir.exists():
            try:
                for file in session_dir.iterdir():
                    if file.is_file():
                        file.unlink()
                session_dir.rmdir()
                logger.info(f"Cleaned up frame files for session {session_id}")
            except Exception as e:
                logger.error(f"Error cleaning up frame files for session {session_id}: {e}")