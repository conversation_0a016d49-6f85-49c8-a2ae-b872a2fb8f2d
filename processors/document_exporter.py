import json
import base64
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging
import io

logger = logging.getLogger(__name__)

try:
    from weasyprint import HTML, CSS
    WEASYPRINT_AVAILABLE = True
    logger.info("WeasyPrint available for PDF generation")
except (ImportError, OSError) as e:
    logger.warning(f"WeasyPrint not available. PDF generation will be limited to HTML: {e}")
    WEASYPRINT_AVAILABLE = False

class DocumentExporter:
    """Export meeting analysis to beautiful PDF, Markdown, and HTML formats"""

    def __init__(self, frames_dir: Path):
        self.frames_dir = Path(frames_dir)

    def _encode_image_to_base64(self, image_path: Path) -> Optional[str]:
        """Encode image to base64 for embedding in HTML/PDF"""
        try:
            if not image_path.exists():
                logger.warning(f"Image not found: {image_path}")
                return None

            with open(image_path, 'rb') as img_file:
                img_data = img_file.read()
                img_base64 = base64.b64encode(img_data).decode('utf-8')

                # Determine MIME type based on extension
                ext = image_path.suffix.lower()
                if ext in ['.jpg', '.jpeg']:
                    mime_type = 'image/jpeg'
                elif ext == '.png':
                    mime_type = 'image/png'
                elif ext == '.gif':
                    mime_type = 'image/gif'
                else:
                    mime_type = 'image/jpeg'  # Default

                return f"data:{mime_type};base64,{img_base64}"

        except Exception as e:
            logger.error(f"Failed to encode image {image_path}: {e}")
            return None

    def export_to_markdown(self, session_data: Dict[str, Any]) -> str:
        """Export session data to beautiful Markdown format"""

        session = session_data.get('session', {})
        transcription = session_data.get('transcription_segments', [])
        frames = session_data.get('extracted_frames', [])
        timeline = session_data.get('timeline_events', [])
        summary = session_data.get('summary', {})

        md_content = []

        # Header
        md_content.extend([
            f"# Meeting Analysis Report",
            f"**File:** {session.get('original_filename', 'Unknown')}",
            f"**Date:** {session.get('created_at', 'Unknown')}",
            f"**Duration:** {self._format_duration(session.get('duration', 0))}",
            f"**Language:** {session.get('language_detected', 'Unknown').upper()}",
            "",
            "---",
            ""
        ])

        # Executive Summary
        if summary and summary.get('executive_summary'):
            md_content.extend([
                "## 📋 Executive Summary",
                "",
                summary['executive_summary'],
                ""
            ])

        # Key Points
        if summary and summary.get('key_points'):
            try:
                key_points = json.loads(summary['key_points']) if isinstance(summary['key_points'], str) else summary['key_points']
                md_content.extend([
                    "## 🎯 Key Discussion Points",
                    ""
                ])
                for point in key_points:
                    md_content.append(f"- {point}")
                md_content.append("")
            except:
                pass

        # Action Items
        if summary and summary.get('action_items'):
            try:
                action_items = json.loads(summary['action_items']) if isinstance(summary['action_items'], str) else summary['action_items']
                md_content.extend([
                    "## ✅ Action Items",
                    ""
                ])
                for item in action_items:
                    md_content.append(f"- [ ] {item}")
                md_content.append("")
            except:
                pass

        # Decisions Made
        if summary and summary.get('decisions'):
            try:
                decisions = json.loads(summary['decisions']) if isinstance(summary['decisions'], str) else summary['decisions']
                if decisions:
                    md_content.extend([
                        "## 🔹 Decisions Made",
                        ""
                    ])
                    for decision in decisions:
                        md_content.append(f"- {decision}")
                    md_content.append("")
            except:
                pass

        # Visual Timeline
        if frames:
            md_content.extend([
                "## 🖼️ Visual Timeline",
                "",
                "Key frames extracted from the meeting:",
                ""
            ])

            # Sort frames by timestamp
            sorted_frames = sorted(frames, key=lambda x: x.get('timestamp', 0))

            for i, frame in enumerate(sorted_frames[:10]):  # Show top 10 frames
                timestamp = self._format_timestamp(frame.get('timestamp', 0))
                frame_type = frame.get('frame_type', 'unknown')
                confidence = frame.get('scene_change_score', 0)

                md_content.extend([
                    f"### Frame {i+1} - {timestamp}",
                    f"**Type:** {frame_type.title()} | **Change Score:** {confidence:.2f}",
                    f"**File:** `{frame.get('filename', 'unknown')}`",
                    ""
                ])

        # Full Transcription
        if transcription:
            md_content.extend([
                "## 📝 Full Transcription",
                "",
                "Complete meeting transcript with timestamps:",
                ""
            ])

            # Group by time intervals for readability
            current_minute = -1

            for segment in transcription:
                segment_minute = int(segment.get('start_time', 0) // 60)

                if segment_minute != current_minute:
                    current_minute = segment_minute
                    md_content.extend([
                        f"### {self._format_timestamp(segment_minute * 60)}",
                        ""
                    ])

                start_time = self._format_timestamp(segment.get('start_time', 0))
                confidence = segment.get('confidence', 1.0)
                text = segment.get('text', '').strip()

                if text:
                    confidence_indicator = "🟢" if confidence > 0.8 else "🟡" if confidence > 0.6 else "🔴"
                    md_content.append(f"**[{start_time}]** {confidence_indicator} {text}")
                    md_content.append("")

        # Statistics
        stats = self._calculate_stats(session_data)
        md_content.extend([
            "## 📊 Meeting Statistics",
            "",
            f"- **Total Duration:** {stats['duration']}",
            f"- **Total Words:** {stats['word_count']:,}",
            f"- **Speech Rate:** {stats['words_per_minute']:.1f} words/minute",
            f"- **Transcription Segments:** {stats['segment_count']}",
            f"- **Key Frames Extracted:** {stats['frame_count']}",
            f"- **Average Confidence:** {stats['avg_confidence']:.1%}",
            ""
        ])

        # Footer
        md_content.extend([
            "---",
            f"*Report generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} by Meeting Transcription & Analysis API*"
        ])

        return "\n".join(md_content)

    def export_to_html(self, session_data: Dict[str, Any]) -> str:
        """Export session data to beautiful HTML format"""

        session = session_data.get('session', {})
        transcription = session_data.get('transcription_segments', [])
        frames = session_data.get('extracted_frames', [])
        summary = session_data.get('summary', {})

        # HTML template with modern styling
        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meeting Analysis Report - {session.get('original_filename', 'Unknown')}</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }}

        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}

        .header {{
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }}

        .header h1 {{
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }}

        .header .meta {{
            font-size: 1.1em;
            opacity: 0.9;
        }}

        .content {{
            padding: 40px;
        }}

        .section {{
            margin-bottom: 40px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 5px solid #3498db;
        }}

        .section h2 {{
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }}

        .section h3 {{
            color: #34495e;
            font-size: 1.3em;
            margin: 20px 0 10px 0;
        }}

        .summary {{
            font-size: 1.1em;
            line-height: 1.8;
            color: #555;
        }}

        .key-points, .action-items, .decisions {{
            list-style: none;
        }}

        .key-points li, .action-items li, .decisions li {{
            padding: 10px 0;
            padding-left: 30px;
            position: relative;
            border-bottom: 1px solid #eee;
        }}

        .key-points li:before {{
            content: "🎯";
            position: absolute;
            left: 0;
        }}

        .action-items li:before {{
            content: "✅";
            position: absolute;
            left: 0;
        }}

        .decisions li:before {{
            content: "🔹";
            position: absolute;
            left: 0;
        }}

        .timeline-item {{
            background: white;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-left: 4px solid #e74c3c;
        }}

        .timestamp {{
            font-weight: bold;
            color: #3498db;
            font-size: 1.1em;
        }}

        .transcription-segment {{
            background: white;
            padding: 15px 20px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 3px solid #27ae60;
        }}

        .confidence-high {{ border-left-color: #27ae60; }}
        .confidence-medium {{ border-left-color: #f39c12; }}
        .confidence-low {{ border-left-color: #e74c3c; }}

        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }}

        .stat-card {{
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }}

        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }}

        .stat-label {{
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }}

        .footer {{
            text-align: center;
            padding: 30px;
            color: #666;
            border-top: 1px solid #eee;
            background: #f8f9fa;
        }}

        .frames-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }}

        .frame-item {{
            background: white;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border: 1px solid #e1e8ed;
        }}

        .frame-header {{
            margin-bottom: 12px;
        }}

        .frame-header .timestamp {{
            font-weight: bold;
            color: #2c3e50;
            font-size: 14px;
            margin-bottom: 5px;
        }}

        .frame-meta {{
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}

        .frame-type {{
            background: #3498db;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }}

        .confidence {{
            background: #f8f9fa;
            color: #6c757d;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }}

        .frame-image {{
            margin-top: 10px;
        }}

        .frame-placeholder {{
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            color: #6c757d;
            margin-top: 10px;
        }}

        @media (max-width: 768px) {{
            .container {{
                margin: 10px;
                border-radius: 10px;
            }}

            .header {{
                padding: 20px;
            }}

            .header h1 {{
                font-size: 2em;
            }}

            .content {{
                padding: 20px;
            }}

            .section {{
                padding: 20px;
            }}

            .frames-grid {{
                grid-template-columns: 1fr;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Meeting Analysis Report</h1>
            <div class="meta">
                <p><strong>{session.get('original_filename', 'Unknown File')}</strong></p>
                <p>{session.get('created_at', 'Unknown Date')} • {self._format_duration(session.get('duration', 0))} • {session.get('language_detected', 'Unknown').upper()}</p>
            </div>
        </div>

        <div class="content">
        """

        # Executive Summary
        if summary and summary.get('executive_summary'):
            html_content += f"""
            <div class="section">
                <h2>📋 Executive Summary</h2>
                <div class="summary">{summary['executive_summary']}</div>
            </div>
            """

        # Key Points
        if summary and summary.get('key_points'):
            try:
                key_points = json.loads(summary['key_points']) if isinstance(summary['key_points'], str) else summary['key_points']
                html_content += f"""
                <div class="section">
                    <h2>🎯 Key Discussion Points</h2>
                    <ul class="key-points">
                """
                for point in key_points:
                    html_content += f"<li>{point}</li>"
                html_content += """
                    </ul>
                </div>
                """
            except:
                pass

        # Action Items
        if summary and summary.get('action_items'):
            try:
                action_items = json.loads(summary['action_items']) if isinstance(summary['action_items'], str) else summary['action_items']
                html_content += f"""
                <div class="section">
                    <h2>✅ Action Items</h2>
                    <ul class="action-items">
                """
                for item in action_items:
                    html_content += f"<li>{item}</li>"
                html_content += """
                    </ul>
                </div>
                """
            except:
                pass

        # Visual Timeline with embedded images
        if frames:
            html_content += """
            <div class="section">
                <h2>🖼️ Visual Timeline</h2>
                <div class="frames-grid">
            """

            sorted_frames = sorted(frames, key=lambda x: x.get('timestamp', 0))

            for i, frame in enumerate(sorted_frames[:8]):  # Show top 8 frames
                timestamp = self._format_timestamp(frame.get('timestamp', 0))
                frame_type = frame.get('frame_type', 'unknown')
                confidence = frame.get('scene_change_score', 0)
                filename = frame.get('filename', 'unknown')

                # Try to embed the actual image
                frame_path = self.frames_dir / filename
                image_data = self._encode_image_to_base64(frame_path)

                html_content += f"""
                <div class="frame-item">
                    <div class="frame-header">
                        <div class="timestamp">Frame {i+1} - {timestamp}</div>
                        <div class="frame-meta">
                            <span class="frame-type">{frame_type.title()}</span>
                            <span class="confidence">Score: {confidence:.2f}</span>
                        </div>
                    </div>
                """

                if image_data:
                    html_content += f"""
                    <div class="frame-image">
                        <img src="{image_data}" alt="Frame {i+1}" style="max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    </div>
                    """
                else:
                    html_content += f"""
                    <div class="frame-placeholder">
                        <p>📷 Frame not available</p>
                        <p><small>{filename}</small></p>
                    </div>
                    """

                html_content += "</div>"

            html_content += """
                </div>
            </div>
            """

        # Transcription Sample
        if transcription:
            html_content += """
            <div class="section">
                <h2>📝 Transcription Sample</h2>
                <p style="margin-bottom: 20px;">Key segments from the meeting transcript:</p>
            """

            # Show first 10 segments
            for segment in transcription[:10]:
                start_time = self._format_timestamp(segment.get('start_time', 0))
                confidence = segment.get('confidence', 1.0)
                text = segment.get('text', '').strip()

                if text:
                    confidence_class = "high" if confidence > 0.8 else "medium" if confidence > 0.6 else "low"
                    html_content += f"""
                    <div class="transcription-segment confidence-{confidence_class}">
                        <div class="timestamp">[{start_time}]</div>
                        <p>{text}</p>
                    </div>
                    """

            html_content += "</div>"

        # Statistics
        stats = self._calculate_stats(session_data)
        html_content += f"""
        <div class="section">
            <h2>📊 Meeting Statistics</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">{stats['duration']}</div>
                    <div class="stat-label">Duration</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{stats['word_count']:,}</div>
                    <div class="stat-label">Total Words</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{stats['words_per_minute']:.0f}</div>
                    <div class="stat-label">Words/Minute</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{stats['segment_count']}</div>
                    <div class="stat-label">Segments</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{stats['frame_count']}</div>
                    <div class="stat-label">Key Frames</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{stats['avg_confidence']:.0%}</div>
                    <div class="stat-label">Avg Confidence</div>
                </div>
            </div>
        </div>
        """

        # Footer
        html_content += f"""
        </div>

        <div class="footer">
            <p>Report generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} by Meeting Transcription & Analysis API</p>
        </div>
    </div>
</body>
</html>
        """

        return html_content

    def export_to_pdf_html(self, session_data: Dict[str, Any]) -> str:
        """Export session data optimized for PDF conversion"""

        # Create a print-optimized HTML version
        html = self.export_to_html(session_data)

        # Add print-specific CSS
        print_css = """
        <style media="print">
            @page {
                margin: 2cm;
                size: A4;
            }

            body {
                background: white !important;
                padding: 0 !important;
            }

            .container {
                box-shadow: none !important;
                border-radius: 0 !important;
                max-width: 100% !important;
            }

            .header {
                background: #2c3e50 !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .section {
                page-break-inside: avoid;
                background: #f8f9fa !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .timeline-item, .transcription-segment {
                page-break-inside: avoid;
            }
        </style>
        """

        # Insert print CSS before closing head tag
        html = html.replace('</head>', print_css + '</head>')

        return html

    def export_to_pdf(self, session_data: Dict[str, Any]) -> bytes:
        """Export session data to actual PDF format"""

        if not WEASYPRINT_AVAILABLE:
            logger.error("WeasyPrint not available. Cannot generate PDF.")
            raise RuntimeError("PDF generation requires WeasyPrint. Install with: pip install weasyprint")

        try:
            # Get HTML content optimized for PDF
            html_content = self.export_to_pdf_html(session_data)

            # Create PDF from HTML
            html_doc = HTML(string=html_content)
            pdf_bytes = html_doc.write_pdf()

            logger.info("PDF generated successfully")
            return pdf_bytes

        except Exception as e:
            logger.error(f"Failed to generate PDF: {e}")
            raise

    def _format_duration(self, seconds: float) -> str:
        """Format duration in human-readable format"""
        if seconds < 60:
            return f"{int(seconds)}s"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            secs = int(seconds % 60)
            return f"{minutes}m {secs}s"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}h {minutes}m"

    def _format_timestamp(self, seconds: float) -> str:
        """Format timestamp as MM:SS or HH:MM:SS"""
        if seconds < 3600:
            minutes = int(seconds // 60)
            secs = int(seconds % 60)
            return f"{minutes:02d}:{secs:02d}"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            secs = int(seconds % 60)
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"

    def _calculate_stats(self, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate meeting statistics"""
        session = session_data.get('session', {})
        transcription = session_data.get('transcription_segments', [])
        frames = session_data.get('extracted_frames', [])

        duration = session.get('duration', 0)

        # Count words
        word_count = 0
        total_confidence = 0

        for segment in transcription:
            text = segment.get('text', '')
            word_count += len(text.split())
            total_confidence += segment.get('confidence', 1.0)

        avg_confidence = total_confidence / len(transcription) if transcription else 0
        words_per_minute = (word_count / duration * 60) if duration > 0 else 0

        return {
            'duration': self._format_duration(duration),
            'word_count': word_count,
            'words_per_minute': words_per_minute,
            'segment_count': len(transcription),
            'frame_count': len(frames),
            'avg_confidence': avg_confidence
        }