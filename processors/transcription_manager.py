import logging
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from .transcription import WhisperTranscriber, TranscriptionSegment
from .aws_transcribe import AWSTranscriber
from .whisper_cpp import WhisperCppTranscriber

logger = logging.getLogger(__name__)

class TranscriptionService(str, Enum):
    WHISPER = "whisper"
    WHISPER_CPP = "whisper_cpp"
    AWS_TRANSCRIBE = "aws_transcribe"

class TranscriptionManager:
    """Manager for different transcription services"""
    
    def __init__(self, service: str = "whisper", **kwargs):
        self.service = TranscriptionService(service)
        self.transcriber = None
        self.kwargs = kwargs
        
    def get_transcriber(self):
        """Get the appropriate transcriber instance"""
        if self.transcriber is None:
            if self.service == TranscriptionService.WHISPER:
                model_name = self.kwargs.get('whisper_model', 'large-v3')
                self.transcriber = WhisperTranscriber(model_name)
                
            elif self.service == TranscriptionService.WHISPER_CPP:
                model_path = self.kwargs.get('whisper_cpp_model_path', '')
                if not model_path:
                    logger.error("Whisper.cpp model path not provided")
                    raise ValueError("Whisper.cpp model path is required")
                self.transcriber = WhisperCppTranscriber(model_path)
                
            elif self.service == TranscriptionService.AWS_TRANSCRIBE:
                aws_config = {
                    'access_key_id': self.kwargs.get('aws_access_key_id', ''),
                    'secret_access_key': self.kwargs.get('aws_secret_access_key', ''),
                    'region': self.kwargs.get('aws_region', 'us-east-1')
                }
                self.transcriber = AWSTranscriber(**aws_config)
                
            else:
                raise ValueError(f"Unsupported transcription service: {self.service}")
                
        return self.transcriber
    
    def transcribe_audio(
        self, 
        audio_path: Path,
        language: Optional[str] = None,
        **kwargs
    ) -> Tuple[List[TranscriptionSegment], str]:
        """
        Transcribe audio using the configured service
        
        Args:
            audio_path: Path to audio file
            language: Language code ("en", "ru", "uk") or None for auto-detect
            **kwargs: Additional service-specific parameters
            
        Returns:
            Tuple of (transcription_segments, detected_language)
        """
        transcriber = self.get_transcriber()
        
        try:
            logger.info(f"Transcribing with {self.service.value}")
            
            if hasattr(transcriber, 'transcribe_audio'):
                return transcriber.transcribe_audio(audio_path, language, **kwargs)
            else:
                logger.error(f"Transcriber {self.service.value} does not support transcribe_audio method")
                return [], "unknown"
                
        except Exception as e:
            logger.error(f"Transcription failed with {self.service.value}: {e}")
            return [], "unknown"
    
    def detect_language(self, audio_path: Path) -> str:
        """Detect language from audio file"""
        transcriber = self.get_transcriber()
        
        try:
            if hasattr(transcriber, 'detect_language'):
                return transcriber.detect_language(audio_path)
            else:
                logger.warning(f"Language detection not supported by {self.service.value}")
                return "en"  # Default fallback
                
        except Exception as e:
            logger.error(f"Language detection failed with {self.service.value}: {e}")
            return "en"
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages for the current service"""
        transcriber = self.get_transcriber()
        
        if hasattr(transcriber, 'supported_languages'):
            return transcriber.supported_languages
        else:
            return ["en", "ru", "uk"]  # Default supported languages
    
    def cleanup(self):
        """Clean up transcriber resources"""
        if self.transcriber and hasattr(self.transcriber, 'unload_model'):
            self.transcriber.unload_model()
        self.transcriber = None
