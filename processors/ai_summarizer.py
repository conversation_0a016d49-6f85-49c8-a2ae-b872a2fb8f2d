import openai
import json
import logging
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
from .transcription import TranscriptionSegment
from .timeline_sync import SynchronizedTimeline, TimelineEvent

logger = logging.getLogger(__name__)

@dataclass
class MeetingSummary:
    """Data class for meeting summary"""
    executive_summary: str
    key_points: List[str]
    action_items: List[str]
    decisions: List[str]
    slide_descriptions: List[Dict[str, Any]]
    topic_timeline: List[Dict[str, Any]]
    participants_mentioned: List[str]
    meeting_type: str
    confidence_score: float

class AISummarizer:
    """AI-powered meeting summarization using OpenAI GPT-4"""

    def __init__(self, api_key: str, model: str = "gpt-4-turbo"):
        self.client = openai.OpenAI(api_key=api_key)
        self.model = model
        self.max_tokens_per_request = 128000  # GPT-4 context limit
        self.chunk_overlap = 300  # Tokens overlap between chunks

    def summarize_meeting(
        self,
        timeline: SynchronizedTimeline,
        session_metadata: Dict[str, Any] = None
    ) -> MeetingSummary:
        """
        Generate comprehensive meeting summary from synchronized timeline

        Args:
            timeline: SynchronizedTimeline with all analysis results
            session_metadata: Additional metadata about the session

        Returns:
            MeetingSummary with all analysis results
        """
        logger.info(f"Generating AI summary for session {timeline.session_id}")

        try:
            # Prepare content for summarization
            content = self._prepare_content_for_summarization(timeline)

            # Check if content needs chunking
            if self._estimate_tokens(content) > self.max_tokens_per_request * 0.7:
                logger.info("Content too large, using chunked summarization")
                summary = self._summarize_large_content(content, timeline)
            else:
                logger.info("Using single-pass summarization")
                summary = self._summarize_content(content, timeline)

            logger.info("AI summarization completed successfully")
            return summary

        except Exception as e:
            logger.error(f"AI summarization failed: {e}")
            return self._create_fallback_summary(timeline)

    def _prepare_content_for_summarization(self, timeline: SynchronizedTimeline) -> str:
        """Prepare structured content for GPT-4 analysis"""

        # Create structured content
        content_parts = []

        # Meeting metadata
        content_parts.append("=== MEETING METADATA ===")
        content_parts.append(f"Duration: {timeline.total_duration:.1f} seconds ({timeline.total_duration/60:.1f} minutes)")
        content_parts.append(f"Total Events: {len(timeline.events)}")
        content_parts.append(f"Transcription Segments: {len(timeline.transcription_segments)}")
        content_parts.append(f"Scene Changes: {timeline.statistics.get('significant_scene_changes', 0)}")
        content_parts.append("")

        # Timeline events with transcription
        content_parts.append("=== TIMELINE WITH TRANSCRIPTION ===")

        for event in timeline.events:
            timestamp_str = self._format_timestamp(event.timestamp)

            if event.transcription_text:
                content_parts.append(f"[{timestamp_str}] SPEECH: {event.transcription_text}")

                if event.transcription_confidence:
                    content_parts.append(f"  Confidence: {event.transcription_confidence:.2f}")

                if event.speaker_id:
                    content_parts.append(f"  Speaker: {event.speaker_id}")

            if event.frame_filename and event.scene_change_score and event.scene_change_score > 0.3:
                content_parts.append(f"[{timestamp_str}] VISUAL CHANGE: {event.frame_type} (score: {event.scene_change_score:.2f})")

            content_parts.append("")

        # Visual analysis summary
        content_parts.append("=== VISUAL ANALYSIS SUMMARY ===")
        slide_events = [e for e in timeline.events if e.frame_type == 'slide']
        speaker_events = [e for e in timeline.events if e.frame_type == 'speaker']
        transition_events = [e for e in timeline.events if e.frame_type == 'transition']

        content_parts.append(f"Presentation slides detected: {len(slide_events)}")
        content_parts.append(f"Speaker views detected: {len(speaker_events)}")
        content_parts.append(f"Transitions detected: {len(transition_events)}")
        content_parts.append("")

        return "\n".join(content_parts)

    def _summarize_content(self, content: str, timeline: SynchronizedTimeline) -> MeetingSummary:
        """Summarize content using single GPT-4 call"""

        # Detect the primary language from transcription
        detected_language = self._detect_primary_language(timeline)
        logger.info(f"Detected primary language for summary: {detected_language}")

        system_prompt = self._get_system_prompt(detected_language)
        user_prompt = self._get_user_prompt(content, timeline, detected_language)

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1,
                max_tokens=2000,
                response_format={"type": "json_object"}
            )

            result = json.loads(response.choices[0].message.content)
            return self._parse_summary_response(result)

        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            raise

    def _summarize_large_content(self, content: str, timeline: SynchronizedTimeline) -> MeetingSummary:
        """Summarize large content using chunked approach"""

        # Detect the primary language
        detected_language = self._detect_primary_language(timeline)

        # Split content into chunks
        chunks = self._split_content_into_chunks(content)

        # Get language-specific system prompt for chunks
        chunk_system_prompt = self._get_chunk_system_prompt(detected_language)

        # Summarize each chunk
        chunk_summaries = []
        for i, chunk in enumerate(chunks):
            logger.info(f"Summarizing chunk {i+1}/{len(chunks)} in {detected_language}")

            chunk_prompt = self._get_chunk_summary_prompt(chunk, i+1, len(chunks), detected_language)

            try:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": chunk_system_prompt},
                        {"role": "user", "content": chunk_prompt}
                    ],
                    temperature=0.1,
                    max_tokens=1000
                )

                chunk_summaries.append(response.choices[0].message.content)

            except Exception as e:
                logger.error(f"Failed to summarize chunk {i+1}: {e}")
                chunk_summaries.append(f"[Error summarizing chunk {i+1}]")

        # Combine chunk summaries into final summary
        combined_content = "\n\n".join([
            "=== CHUNK SUMMARIES ===",
            *[f"Chunk {i+1}: {summary}" for i, summary in enumerate(chunk_summaries)],
            "",
            "=== ORIGINAL TIMELINE STATISTICS ===",
            f"Total duration: {timeline.total_duration/60:.1f} minutes",
            f"Total events: {len(timeline.events)}",
            f"Transcription segments: {len(timeline.transcription_segments)}"
        ])

        return self._summarize_content(combined_content, timeline)

    def _get_system_prompt(self, language: str = "en") -> str:
        """Get the system prompt for meeting summarization in the detected language"""

        language_instructions = {
            "en": """You are an expert meeting analyst specializing in video conference and presentation analysis.
            You analyze meeting transcripts with visual scene information to create comprehensive summaries.

            Your analysis should focus on:
            1. Key discussion points and topics
            2. Decisions made and action items
            3. Presentation flow and visual changes
            4. Speaker dynamics and engagement
            5. Meeting structure and organization

            Always provide structured JSON output with the requested fields.
            Be concise but comprehensive, focusing on business value and actionable insights.
            Respond in English.""",

            "ru": """Вы эксперт-аналитик совещаний, специализирующийся на анализе видеоконференций и презентаций.
            Вы анализируете расшифровки совещаний с информацией о визуальных сценах для создания всесторонних резюме.

            Ваш анализ должен сосредоточиться на:
            1. Ключевые темы обсуждения
            2. Принятые решения и пункты действий
            3. Ход презентации и визуальные изменения
            4. Динамика выступающих и вовлеченность
            5. Структура и организация совещания

            Всегда предоставляйте структурированный JSON-вывод с запрошенными полями.
            Будьте лаконичны, но всесторонни, сосредоточьтесь на бизнес-ценности и практических выводах.
            Отвечайте на русском языке.""",

            "uk": """Ви експерт-аналітик нарад, що спеціалізується на аналізі відеоконференцій та презентацій.
            Ви аналізуєте розшифровки нарад з інформацією про візуальні сцени для створення всебічних резюме.

            Ваш аналіз повинен зосередитися на:
            1. Ключові теми обговорення
            2. Прийняті рішення та пункти дій
            3. Хід презентації та візуальні зміни
            4. Динаміка виступаючих та залученість
            5. Структура та організація наради

            Завжди надавайте структурований JSON-вивід із запитаними полями.
            Будьте лаконічними, але всебічними, зосередьтеся на бізнес-цінності та практичних висновках.
            Відповідайте українською мовою."""
        }

        return language_instructions.get(language, language_instructions["en"])

    def _get_user_prompt(self, content: str, timeline: SynchronizedTimeline, language: str = "en") -> str:
        """Get the user prompt for meeting summarization in the detected language"""

        language_prompts = {
            "en": f"""Analyze this meeting recording and provide a comprehensive summary in JSON format.

{content}

Please provide your analysis in the following JSON structure:
{{
    "executive_summary": "2-3 sentence overview of the meeting",
    "key_points": ["list", "of", "main", "discussion", "points"],
    "action_items": ["list", "of", "specific", "action", "items"],
    "decisions": ["list", "of", "decisions", "made"],
    "slide_descriptions": [
        {{"timestamp": "MM:SS", "description": "what was shown", "key_content": "main points"}}
    ],
    "topic_timeline": [
        {{"timestamp": "MM:SS", "topic": "topic name", "duration": "duration in minutes", "summary": "brief summary"}}
    ],
    "participants_mentioned": ["list", "of", "people", "mentioned"],
    "meeting_type": "presentation/discussion/training/other",
    "confidence_score": 0.0-1.0
}}

Focus on extracting actionable business value from both the spoken content and visual presentation changes.""",

            "ru": f"""Проанализируйте эту запись совещания и предоставьте всестороннее резюме в формате JSON.

{content}

Пожалуйста, предоставьте ваш анализ в следующей JSON структуре:
{{
    "executive_summary": "Обзор совещания в 2-3 предложениях",
    "key_points": ["список", "основных", "тем", "обсуждения"],
    "action_items": ["список", "конкретных", "пунктов", "действий"],
    "decisions": ["список", "принятых", "решений"],
    "slide_descriptions": [
        {{"timestamp": "ММ:СС", "description": "что было показано", "key_content": "основные моменты"}}
    ],
    "topic_timeline": [
        {{"timestamp": "ММ:СС", "topic": "название темы", "duration": "продолжительность в минутах", "summary": "краткое резюме"}}
    ],
    "participants_mentioned": ["список", "упомянутых", "людей"],
    "meeting_type": "презентация/обсуждение/обучение/другое",
    "confidence_score": 0.0-1.0
}}

Сосредоточьтесь на извлечении практической бизнес-ценности как из устного содержания, так и из визуальных изменений презентации.""",

            "uk": f"""Проаналізуйте цей запис наради та надайте всебічне резюме у форматі JSON.

{content}

Будь ласка, надайте ваш аналіз у наступній JSON структурі:
{{
    "executive_summary": "Огляд наради у 2-3 реченнях",
    "key_points": ["список", "основних", "тем", "обговорення"],
    "action_items": ["список", "конкретних", "пунктів", "дій"],
    "decisions": ["список", "прийнятих", "рішень"],
    "slide_descriptions": [
        {{"timestamp": "ХХ:СС", "description": "що було показано", "key_content": "основні моменти"}}
    ],
    "topic_timeline": [
        {{"timestamp": "ХХ:СС", "topic": "назва теми", "duration": "тривалість у хвилинах", "summary": "коротке резюме"}}
    ],
    "participants_mentioned": ["список", "згаданих", "людей"],
    "meeting_type": "презентація/обговорення/навчання/інше",
    "confidence_score": 0.0-1.0
}}

Зосередьтеся на вилученні практичної бізнес-цінності як з усного змісту, так і з візуальних змін презентації."""
        }

        return language_prompts.get(language, language_prompts["en"])

    def _get_chunk_system_prompt(self, language: str = "en") -> str:
        """Get system prompt for chunk summarization in the detected language"""

        language_prompts = {
            "en": "You are an expert meeting analyst. Summarize this portion of a meeting transcript in English.",
            "ru": "Вы эксперт-аналитик совещаний. Кратко изложите эту часть расшифровки совещания на русском языке.",
            "uk": "Ви експерт-аналітик нарад. Коротко викладіть цю частину розшифровки наради українською мовою."
        }

        return language_prompts.get(language, language_prompts["en"])

    def _get_chunk_summary_prompt(self, chunk: str, chunk_num: int, total_chunks: int, language: str = "en") -> str:
        """Get prompt for summarizing individual chunks in the detected language"""

        language_prompts = {
            "en": f"""Summarize this portion of a meeting (chunk {chunk_num} of {total_chunks}):

{chunk}

Provide a concise summary focusing on:
- Key topics discussed
- Important decisions or action items
- Notable presentation changes
- Speaker interactions

Keep the summary under 200 words.""",

            "ru": f"""Кратко изложите эту часть совещания (часть {chunk_num} из {total_chunks}):

{chunk}

Предоставьте краткое резюме, сосредоточившись на:
- Ключевые обсуждаемые темы
- Важные решения или пункты действий
- Заметные изменения в презентации
- Взаимодействие выступающих

Ограничьте резюме 200 словами.""",

            "uk": f"""Коротко викладіть цю частину наради (частина {chunk_num} з {total_chunks}):

{chunk}

Надайте стисле резюме, зосередившись на:
- Ключові теми обговорення
- Важливі рішення або пункти дій
- Помітні зміни в презентації
- Взаємодія виступаючих

Обмежте резюме 200 словами."""
        }

        return language_prompts.get(language, language_prompts["en"])

    def _split_content_into_chunks(self, content: str) -> List[str]:
        """Split content into manageable chunks for processing"""
        words = content.split()
        chunk_size = 3000  # Approximate words per chunk
        chunks = []

        for i in range(0, len(words), chunk_size - self.chunk_overlap):
            chunk_words = words[i:i + chunk_size]
            chunks.append(' '.join(chunk_words))

        return chunks

    def _parse_summary_response(self, result: Dict) -> MeetingSummary:
        """Parse GPT-4 response into MeetingSummary object"""
        return MeetingSummary(
            executive_summary=result.get("executive_summary", ""),
            key_points=result.get("key_points", []),
            action_items=result.get("action_items", []),
            decisions=result.get("decisions", []),
            slide_descriptions=result.get("slide_descriptions", []),
            topic_timeline=result.get("topic_timeline", []),
            participants_mentioned=result.get("participants_mentioned", []),
            meeting_type=result.get("meeting_type", "unknown"),
            confidence_score=result.get("confidence_score", 0.0)
        )

    def _create_fallback_summary(self, timeline: SynchronizedTimeline) -> MeetingSummary:
        """Create basic summary when AI processing fails"""
        logger.warning("Creating fallback summary due to AI processing failure")

        # Extract basic information from timeline
        total_text = " ".join([
            segment.text for segment in timeline.transcription_segments
            if segment.text
        ])

        word_count = len(total_text.split())

        return MeetingSummary(
            executive_summary=f"Meeting recorded with {timeline.total_duration/60:.1f} minutes of content, {word_count} words transcribed.",
            key_points=[f"Meeting duration: {timeline.total_duration/60:.1f} minutes"],
            action_items=["Review AI processing logs for technical issues"],
            decisions=[],
            slide_descriptions=[],
            topic_timeline=[],
            participants_mentioned=[],
            meeting_type="unknown",
            confidence_score=0.1
        )

    def _estimate_tokens(self, text: str) -> int:
        """Rough estimation of token count (1 token ≈ 0.75 words)"""
        return int(len(text.split()) / 0.75)

    def _detect_primary_language(self, timeline: SynchronizedTimeline) -> str:
        """Detect the primary language from transcription segments"""
        if not timeline.transcription_segments:
            return "en"  # Default to English

        # Count language occurrences weighted by segment duration
        language_weights = {}

        for segment in timeline.transcription_segments:
            language = segment.language or "en"
            duration = segment.end_time - segment.start_time

            if language in language_weights:
                language_weights[language] += duration
            else:
                language_weights[language] = duration

        # Return the language with the most total duration
        if language_weights:
            primary_language = max(language_weights, key=language_weights.get)
            logger.info(f"Language distribution: {language_weights}")
            return primary_language

        return "en"

    def _format_timestamp(self, seconds: float) -> str:
        """Format seconds to MM:SS format"""
        minutes = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{minutes:02d}:{secs:02d}"

    def generate_presentation_summary(self, timeline: SynchronizedTimeline) -> Dict:
        """Generate focused summary for presentation-style meetings"""

        # Extract slide information
        slides = []
        for event in timeline.events:
            if event.frame_type == 'slide' and event.transcription_text:
                slides.append({
                    'timestamp': self._format_timestamp(event.timestamp),
                    'content': event.transcription_text[:200] + "..." if len(event.transcription_text) > 200 else event.transcription_text,
                    'frame': event.frame_filename
                })

        return {
            'presentation_type': 'slide_based',
            'total_slides': len(slides),
            'slides': slides,
            'duration': timeline.total_duration,
            'key_topics': self._extract_key_topics(timeline.transcription_segments)
        }

    def _extract_key_topics(self, segments: List[TranscriptionSegment]) -> List[str]:
        """Simple keyword extraction for key topics"""
        # This is a simplified version - in production, use more sophisticated NLP
        all_text = " ".join([segment.text for segment in segments])
        words = all_text.lower().split()

        # Simple frequency-based topic extraction
        word_freq = {}
        for word in words:
            if len(word) > 4:  # Filter short words
                word_freq[word] = word_freq.get(word, 0) + 1

        # Return top 10 most frequent words as topics
        topics = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]
        return [topic[0] for topic in topics]