import boto3
import json
import time
import logging
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
import uuid

from .transcription import TranscriptionSegment

logger = logging.getLogger(__name__)

class AWSTranscriber:
    """AWS Transcribe service for speech-to-text"""
    
    def __init__(self, access_key_id: str, secret_access_key: str, region: str = "us-east-1"):
        self.access_key_id = access_key_id
        self.secret_access_key = secret_access_key
        self.region = region
        self.supported_languages = ["en", "ru", "uk"]
        
        # Language mapping for AWS Transcribe
        self.language_mapping = {
            "en": "en-US",
            "ru": "ru-RU", 
            "uk": "uk-UA"
        }
        
        # Initialize AWS clients
        self._init_clients()
    
    def _init_clients(self):
        """Initialize AWS service clients"""
        try:
            session = boto3.Session(
                aws_access_key_id=self.access_key_id,
                aws_secret_access_key=self.secret_access_key,
                region_name=self.region
            )
            
            self.transcribe_client = session.client('transcribe')
            self.s3_client = session.client('s3')
            
            # Create a bucket name for temporary files
            self.bucket_name = f"meeting-transcribe-temp-{uuid.uuid4().hex[:8]}"
            
            logger.info("AWS Transcribe client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize AWS clients: {e}")
            raise
    
    def transcribe_audio(
        self, 
        audio_path: Path,
        language: Optional[str] = None,
        **kwargs
    ) -> Tuple[List[TranscriptionSegment], str]:
        """
        Transcribe audio using AWS Transcribe
        
        Args:
            audio_path: Path to audio file
            language: Language code ("en", "ru", "uk") or None for auto-detect
            
        Returns:
            Tuple of (transcription_segments, detected_language)
        """
        try:
            logger.info(f"Starting AWS Transcribe for {audio_path}")
            
            # Detect language if not provided
            if not language:
                language = self.detect_language(audio_path)
                logger.info(f"Auto-detected language: {language}")
            
            # Map language to AWS format
            aws_language = self.language_mapping.get(language, "en-US")
            
            # Upload audio to S3
            s3_uri = self._upload_to_s3(audio_path)
            
            # Start transcription job
            job_name = f"transcribe-job-{uuid.uuid4().hex}"
            
            response = self.transcribe_client.start_transcription_job(
                TranscriptionJobName=job_name,
                Media={'MediaFileUri': s3_uri},
                MediaFormat='wav',
                LanguageCode=aws_language,
                Settings={
                    'ShowSpeakerLabels': True,
                    'MaxSpeakerLabels': 10,
                    'ShowAlternatives': True,
                    'MaxAlternatives': 3
                }
            )
            
            # Wait for completion
            segments = self._wait_for_completion(job_name)
            
            # Cleanup
            self._cleanup_s3_file(s3_uri)
            
            logger.info(f"AWS Transcribe completed: {len(segments)} segments")
            return segments, language
            
        except Exception as e:
            logger.error(f"AWS Transcribe failed: {e}")
            return [], "unknown"
    
    def _upload_to_s3(self, audio_path: Path) -> str:
        """Upload audio file to S3 for transcription"""
        try:
            # Create bucket if it doesn't exist
            try:
                self.s3_client.create_bucket(Bucket=self.bucket_name)
            except self.s3_client.exceptions.BucketAlreadyExists:
                pass
            except self.s3_client.exceptions.BucketAlreadyOwnedByYou:
                pass
            
            # Upload file
            key = f"audio/{audio_path.name}"
            self.s3_client.upload_file(str(audio_path), self.bucket_name, key)
            
            s3_uri = f"s3://{self.bucket_name}/{key}"
            logger.info(f"Uploaded audio to S3: {s3_uri}")
            return s3_uri
            
        except Exception as e:
            logger.error(f"Failed to upload to S3: {e}")
            raise
    
    def _wait_for_completion(self, job_name: str, max_wait_time: int = 1800) -> List[TranscriptionSegment]:
        """Wait for transcription job to complete and process results"""
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            response = self.transcribe_client.get_transcription_job(
                TranscriptionJobName=job_name
            )
            
            status = response['TranscriptionJob']['TranscriptionJobStatus']
            
            if status == 'COMPLETED':
                # Get transcription results
                result_uri = response['TranscriptionJob']['Transcript']['TranscriptFileUri']
                return self._process_transcription_results(result_uri)
                
            elif status == 'FAILED':
                failure_reason = response['TranscriptionJob'].get('FailureReason', 'Unknown error')
                logger.error(f"AWS Transcribe job failed: {failure_reason}")
                return []
                
            else:
                logger.info(f"Transcription job status: {status}")
                time.sleep(10)  # Wait 10 seconds before checking again
        
        logger.error("Transcription job timed out")
        return []
    
    def _process_transcription_results(self, result_uri: str) -> List[TranscriptionSegment]:
        """Process AWS Transcribe results into our format"""
        try:
            import requests
            
            # Download results
            response = requests.get(result_uri)
            results = response.json()
            
            segments = []
            
            # Process items (words with timestamps)
            items = results.get('results', {}).get('items', [])
            
            # Group words into segments (sentences)
            current_segment = []
            current_start = None
            current_end = None
            
            for item in items:
                if item['type'] == 'pronunciation':
                    word_info = {
                        'word': item['alternatives'][0]['content'],
                        'start': float(item.get('start_time', 0)),
                        'end': float(item.get('end_time', 0)),
                        'confidence': float(item['alternatives'][0].get('confidence', 1.0))
                    }
                    
                    if current_start is None:
                        current_start = word_info['start']
                    
                    current_segment.append(word_info)
                    current_end = word_info['end']
                    
                elif item['type'] == 'punctuation' and current_segment:
                    # End of sentence - create segment
                    text = ' '.join([w['word'] for w in current_segment])
                    if item['alternatives'][0]['content'] in '.!?':
                        text += item['alternatives'][0]['content']
                    
                    avg_confidence = sum(w['confidence'] for w in current_segment) / len(current_segment)
                    
                    segment = TranscriptionSegment(
                        start_time=current_start,
                        end_time=current_end,
                        text=text,
                        confidence=avg_confidence,
                        language=results.get('results', {}).get('language_code', 'en-US')[:2],
                        words=current_segment
                    )
                    
                    segments.append(segment)
                    
                    # Reset for next segment
                    current_segment = []
                    current_start = None
                    current_end = None
            
            # Handle remaining words if any
            if current_segment:
                text = ' '.join([w['word'] for w in current_segment])
                avg_confidence = sum(w['confidence'] for w in current_segment) / len(current_segment)
                
                segment = TranscriptionSegment(
                    start_time=current_start,
                    end_time=current_end,
                    text=text,
                    confidence=avg_confidence,
                    language=results.get('results', {}).get('language_code', 'en-US')[:2],
                    words=current_segment
                )
                
                segments.append(segment)
            
            return segments
            
        except Exception as e:
            logger.error(f"Failed to process transcription results: {e}")
            return []
    
    def _cleanup_s3_file(self, s3_uri: str):
        """Clean up temporary S3 file"""
        try:
            # Extract bucket and key from URI
            parts = s3_uri.replace('s3://', '').split('/', 1)
            bucket = parts[0]
            key = parts[1]
            
            self.s3_client.delete_object(Bucket=bucket, Key=key)
            logger.info(f"Cleaned up S3 file: {s3_uri}")
            
        except Exception as e:
            logger.warning(f"Failed to cleanup S3 file: {e}")
    
    def detect_language(self, audio_path: Path) -> str:
        """
        Simple language detection for AWS Transcribe
        Note: AWS Transcribe has built-in language detection, but for consistency
        we'll use a simple heuristic or default to English
        """
        # For now, default to English
        # In production, you might want to use AWS Transcribe's language identification
        return "en"
