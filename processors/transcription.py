import whisper_timestamped as whisper
import logging
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class TranscriptionSegment:
    """Data class for transcription segments"""
    start_time: float
    end_time: float
    text: str
    confidence: float
    language: str
    words: List[Dict] = None

class WhisperTranscriber:
    """Advanced Whisper transcription with word-level timestamps"""

    def __init__(self, model_name: str = "large-v3"):
        self.model_name = model_name
        self.model = None
        self.supported_languages = ["en", "ru", "uk"]

    def load_model(self) -> bool:
        """Load Whisper model into memory"""
        try:
            logger.info(f"Loading Whisper model: {self.model_name}")
            self.model = whisper.load_model(self.model_name)
            logger.info("Whisper model loaded successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to load Whisper model: {e}")
            return False

    def transcribe_audio(
        self,
        audio_path: Path,
        language: Optional[str] = None,
        enable_vad: bool = True
    ) -> <PERSON><PERSON>[List[TranscriptionSegment], str]:
        """
        Transcribe audio file with enhanced timestamping and chunking for long files

        Args:
            audio_path: Path to audio file
            language: Language code ("en", "ru", "uk") or None for auto-detect
            enable_vad: Enable Voice Activity Detection

        Returns:
            Tuple of (transcription_segments, detected_language)
        """
        if not self.model:
            if not self.load_model():
                return [], "unknown"

        try:
            logger.info(f"Starting transcription of {audio_path}")

            # Detect language first if not specified
            if not language:
                detected_lang = self.detect_language(audio_path)
                logger.info(f"Auto-detected language: {detected_lang}")
                if detected_lang in self.supported_languages:
                    language = detected_lang

            # Check audio duration and decide on chunking strategy
            import whisper
            audio = whisper.load_audio(str(audio_path))
            duration = len(audio) / 16000  # 16kHz sample rate

            logger.info(f"Audio duration: {duration:.2f} seconds")

            if duration > 300:  # 5 minutes - use chunking for long files
                logger.info("Using chunked transcription for long audio file")
                segments, detected_language = self._transcribe_chunked(audio_path, language, enable_vad)
            else:
                logger.info("Using standard transcription for short audio file")
                segments, detected_language = self._transcribe_standard(audio_path, language, enable_vad)

            logger.info(f"Transcription completed: {len(segments)} segments")
            return segments, detected_language

        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            return [], "unknown"

    def _transcribe_standard(
        self,
        audio_path: Path,
        language: Optional[str] = None,
        enable_vad: bool = True
    ) -> Tuple[List[TranscriptionSegment], str]:
        """Standard transcription for shorter files"""
        try:
            # Try with whisper-timestamped first
            try:
                result = whisper.transcribe(
                    self.model,
                    str(audio_path),
                    language=language,
                    vad=enable_vad,
                    word_timestamps=True,
                    condition_on_previous_text=False,  # Prevent repetition
                    compression_ratio_threshold=2.4,
                    logprob_threshold=-1.0,
                    no_speech_threshold=0.6
                )
            except Exception as e:
                logger.warning(f"Advanced transcription failed, trying basic mode: {e}")
                # Fallback to basic whisper
                import whisper as basic_whisper
                result = basic_whisper.transcribe(
                    self.model,
                    str(audio_path),
                    language=language,
                    condition_on_previous_text=False,
                    compression_ratio_threshold=2.4,
                    logprob_threshold=-1.0,
                    no_speech_threshold=0.6
                )

            # Extract detected language
            detected_language = result.get("language", language or "unknown")
            logger.info(f"Detected language: {detected_language}")

            # Process segments
            segments = self._process_segments(result.get("segments", []), detected_language)

            return segments, detected_language

        except Exception as e:
            logger.error(f"Standard transcription failed: {e}")
            return [], "unknown"

    def _transcribe_chunked(
        self,
        audio_path: Path,
        language: Optional[str] = None,
        enable_vad: bool = True
    ) -> Tuple[List[TranscriptionSegment], str]:
        """Chunked transcription for longer files to avoid single phrase issue"""
        try:
            import whisper

            # Load audio
            audio = whisper.load_audio(str(audio_path))
            duration = len(audio) / 16000

            # Chunk parameters
            chunk_duration = 240  # 4 minutes per chunk
            overlap_duration = 30  # 30 seconds overlap
            sample_rate = 16000

            chunk_samples = chunk_duration * sample_rate
            overlap_samples = overlap_duration * sample_rate

            all_segments = []
            detected_language = language or "unknown"

            # Process in chunks
            start_sample = 0
            chunk_index = 0

            while start_sample < len(audio):
                end_sample = min(start_sample + chunk_samples, len(audio))
                chunk_audio = audio[start_sample:end_sample]

                if len(chunk_audio) < sample_rate:  # Skip very short chunks
                    break

                logger.info(f"Processing chunk {chunk_index + 1}: {start_sample/sample_rate:.1f}s - {end_sample/sample_rate:.1f}s")

                try:
                    # Transcribe chunk
                    chunk_audio = whisper.pad_or_trim(chunk_audio)

                    if enable_vad:
                        try:
                            result = whisper.transcribe(
                                self.model,
                                chunk_audio,
                                language=language,
                                vad=True,
                                word_timestamps=True,
                                condition_on_previous_text=False,
                                compression_ratio_threshold=2.4,
                                logprob_threshold=-1.0,
                                no_speech_threshold=0.6
                            )
                        except:
                            # Fallback without VAD
                            result = whisper.transcribe(
                                self.model,
                                chunk_audio,
                                language=language,
                                condition_on_previous_text=False
                            )
                    else:
                        result = whisper.transcribe(
                            self.model,
                            chunk_audio,
                            language=language,
                            condition_on_previous_text=False
                        )

                    # Update detected language from first chunk
                    if chunk_index == 0:
                        detected_language = result.get("language", language or "unknown")
                        logger.info(f"Detected language from first chunk: {detected_language}")

                    # Process chunk segments
                    chunk_segments = self._process_segments(result.get("segments", []), detected_language)

                    # Adjust timestamps for chunk offset
                    chunk_start_time = start_sample / sample_rate
                    for segment in chunk_segments:
                        segment.start_time += chunk_start_time
                        segment.end_time += chunk_start_time

                        # Adjust word timestamps too
                        if segment.words:
                            for word in segment.words:
                                word['start'] += chunk_start_time
                                word['end'] += chunk_start_time

                    # Add to all segments (handle overlap later)
                    all_segments.extend(chunk_segments)

                except Exception as e:
                    logger.warning(f"Failed to process chunk {chunk_index}: {e}")

                # Move to next chunk
                start_sample += chunk_samples - overlap_samples
                chunk_index += 1

            # Remove overlapping segments and merge
            all_segments = self._merge_overlapping_segments(all_segments)

            logger.info(f"Chunked transcription completed: {len(all_segments)} segments from {chunk_index} chunks")
            return all_segments, detected_language

        except Exception as e:
            logger.error(f"Chunked transcription failed: {e}")
            return [], "unknown"

    def _merge_overlapping_segments(self, segments: List[TranscriptionSegment]) -> List[TranscriptionSegment]:
        """Merge overlapping segments from chunked transcription"""
        if not segments:
            return []

        # Sort by start time
        segments.sort(key=lambda x: x.start_time)

        merged = []
        current = segments[0]

        for next_segment in segments[1:]:
            # If segments overlap significantly, merge them
            if (next_segment.start_time <= current.end_time and
                abs(next_segment.start_time - current.end_time) < 5.0):  # 5 second tolerance

                # Extend current segment
                current.end_time = max(current.end_time, next_segment.end_time)

                # Merge text if different
                if next_segment.text.strip() and next_segment.text.strip() not in current.text:
                    current.text += " " + next_segment.text.strip()

                # Merge words
                if next_segment.words:
                    current.words.extend(next_segment.words)

                # Update confidence (average)
                current.confidence = (current.confidence + next_segment.confidence) / 2

            else:
                # No overlap, add current and move to next
                merged.append(current)
                current = next_segment

        # Add the last segment
        merged.append(current)

        return merged

    def _process_segments(self, raw_segments: List[Dict], language: str) -> List[TranscriptionSegment]:
        """Process raw Whisper segments into structured format"""
        segments = []

        for segment in raw_segments:
            try:
                # Extract word-level information if available
                words = []
                if "words" in segment:
                    for word in segment["words"]:
                        words.append({
                            "word": word.get("text", "").strip(),
                            "start": word.get("start", 0.0),
                            "end": word.get("end", 0.0),
                            "confidence": word.get("confidence", 1.0)  # Default confidence if not available
                        })

                # Get confidence from segment or calculate from words
                segment_confidence = segment.get("confidence", 1.0)
                if segment_confidence is None and words:
                    # Calculate average confidence from words
                    confidences = [w.get("confidence", 1.0) for w in words if w.get("confidence") is not None]
                    segment_confidence = sum(confidences) / len(confidences) if confidences else 1.0
                elif segment_confidence is None:
                    segment_confidence = 1.0

                # Create segment
                transcription_segment = TranscriptionSegment(
                    start_time=segment.get("start", 0.0),
                    end_time=segment.get("end", 0.0),
                    text=segment.get("text", "").strip(),
                    confidence=segment_confidence,
                    language=language,
                    words=words
                )

                # Only add non-empty segments
                if transcription_segment.text:
                    segments.append(transcription_segment)

            except Exception as e:
                logger.warning(f"Error processing segment: {e}")
                continue

        return segments

    def detect_language(self, audio_path: Path) -> str:
        """Detect language from audio file with improved Russian detection"""
        if not self.model:
            if not self.load_model():
                return "unknown"

        try:
            # Use Whisper's built-in language detection on multiple segments
            import whisper

            audio = whisper.load_audio(str(audio_path))

            # Test multiple segments for better language detection
            segment_duration = 30.0  # 30 seconds
            sr = 16000
            segment_samples = int(segment_duration * sr)

            language_votes = {}

            # Analyze up to 3 segments
            for i in range(min(3, len(audio) // segment_samples)):
                start_sample = i * segment_samples
                end_sample = min(start_sample + segment_samples, len(audio))
                audio_segment = audio[start_sample:end_sample]

                if len(audio_segment) < sr:  # Skip very short segments
                    continue

                audio_segment = whisper.pad_or_trim(audio_segment)
                mel = whisper.log_mel_spectrogram(audio_segment).to(self.model.device)

                _, probs = self.model.detect_language(mel)

                # Get top 3 languages for this segment
                top_langs = sorted(probs.items(), key=lambda x: x[1], reverse=True)[:3]

                for lang, confidence in top_langs:
                    if lang not in language_votes:
                        language_votes[lang] = []
                    language_votes[lang].append(confidence)

            # Calculate average confidence for each language
            lang_avg_confidence = {}
            for lang, confidences in language_votes.items():
                lang_avg_confidence[lang] = sum(confidences) / len(confidences)

            # Get the most confident language
            if lang_avg_confidence:
                detected_language = max(lang_avg_confidence, key=lang_avg_confidence.get)
                confidence = lang_avg_confidence[detected_language]

                logger.info(f"Language detection results:")
                for lang, conf in sorted(lang_avg_confidence.items(), key=lambda x: x[1], reverse=True)[:3]:
                    logger.info(f"  {lang}: {conf:.3f}")

                # Special handling for Russian/Ukrainian vs English
                if detected_language == "en" and ("ru" in lang_avg_confidence or "uk" in lang_avg_confidence):
                    ru_conf = lang_avg_confidence.get("ru", 0)
                    uk_conf = lang_avg_confidence.get("uk", 0)
                    en_conf = lang_avg_confidence.get("en", 0)

                    # If Russian or Ukrainian is close to English confidence, prefer them
                    if ru_conf > 0.1 and (en_conf - ru_conf) < 0.2:
                        logger.info(f"Switching from EN to RU (en:{en_conf:.3f} vs ru:{ru_conf:.3f})")
                        detected_language = "ru"
                    elif uk_conf > 0.1 and (en_conf - uk_conf) < 0.2:
                        logger.info(f"Switching from EN to UK (en:{en_conf:.3f} vs uk:{uk_conf:.3f})")
                        detected_language = "uk"

                logger.info(f"Final detected language: {detected_language} (confidence: {lang_avg_confidence[detected_language]:.3f})")

                # Return only if it's a supported language
                if detected_language in self.supported_languages:
                    return detected_language
                else:
                    logger.warning(f"Detected language {detected_language} not in supported list")
                    # Check if we have Russian or Ukrainian in top candidates
                    for lang in ["ru", "uk", "en"]:
                        if lang in lang_avg_confidence:
                            logger.info(f"Falling back to {lang}")
                            return lang
                    return "en"  # Final fallback
            else:
                logger.warning("No language detection results")
                return "en"

        except Exception as e:
            logger.error(f"Language detection failed: {e}")
            return "en"  # Fallback to English

    def transcribe_with_speaker_detection(
        self,
        audio_path: Path,
        language: Optional[str] = None
    ) -> Tuple[List[TranscriptionSegment], str]:
        """
        Transcribe with basic speaker change detection
        This is a simplified version - full speaker diarization would require additional models
        """
        segments, detected_language = self.transcribe_audio(audio_path, language)

        # Simple speaker change detection based on silence gaps and audio changes
        # In a production system, this would use a dedicated speaker diarization model
        processed_segments = []
        current_speaker = 1

        for i, segment in enumerate(segments):
            # Simple heuristic: change speaker if there's a long pause (>2 seconds)
            if i > 0:
                prev_segment = segments[i-1]
                pause_duration = segment.start_time - prev_segment.end_time

                if pause_duration > 2.0:  # 2 second threshold
                    current_speaker = 1 if current_speaker == 2 else 2

            # This would be replaced with actual speaker ID from diarization
            segment.speaker_id = current_speaker
            processed_segments.append(segment)

        return processed_segments, detected_language

    def get_transcription_stats(self, segments: List[TranscriptionSegment]) -> Dict:
        """Get statistics about the transcription"""
        if not segments:
            return {}

        total_duration = segments[-1].end_time - segments[0].start_time
        total_words = sum(len(segment.words) for segment in segments if segment.words)
        avg_confidence = sum(segment.confidence for segment in segments) / len(segments)

        return {
            "total_segments": len(segments),
            "total_duration": total_duration,
            "total_words": total_words,
            "average_confidence": avg_confidence,
            "words_per_minute": (total_words / total_duration) * 60 if total_duration > 0 else 0
        }

    def unload_model(self):
        """Free model memory"""
        if self.model:
            del self.model
            self.model = None
            logger.info("Whisper model unloaded from memory")