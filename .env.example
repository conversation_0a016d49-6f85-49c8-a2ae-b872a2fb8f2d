# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# AWS Configuration (for AWS Transcribe)
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1

# Database Configuration
DATABASE_URL=sqlite:///./meeting_analysis.db

# Redis Configuration (for background tasks)
REDIS_URL=redis://localhost:6379/0

# File Upload Settings
MAX_FILE_SIZE_MB=500
ALLOWED_VIDEO_EXTENSIONS=mp4,mov,avi,mkv

# Transcription Settings
TRANSCRIPTION_SERVICE=whisper  # Options: whisper, whisper_cpp, aws_transcribe
WHISPER_MODEL=large-v3
WHISPER_CPP_MODEL_PATH=/path/to/whisper.cpp/models/ggml-large-v3.bin

# Frame Analysis Settings
SCENE_DETECTION_THRESHOLD=27.0
FRAME_EXTRACTION_INTERVAL=10
BLACK_SCREEN_THRESHOLD=15.0