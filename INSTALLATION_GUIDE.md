# Installation Guide for Enhanced Meeting Analysis API

This guide covers installation of the enhanced meeting analysis system with multiple transcription services.

## 🚀 Quick Start

### 1. Basic Installation

```bash
# Clone the repository
git clone <repository-url>
cd sim-presentation-analisys

# Install Python dependencies
pip install -r requirements.txt

# Copy environment configuration
cp .env.example .env
# Edit .env with your API keys and preferences
```

### 2. Configure Transcription Service

Choose one or more transcription services:

#### Option A: OpenAI Whisper (Default)
```bash
# Already included in requirements.txt
# No additional setup required
echo "TRANSCRIPTION_SERVICE=whisper" >> .env
```

#### Option B: Whisper.cpp (Fastest)
```bash
# Install whisper.cpp
git clone https://github.com/ggerganov/whisper.cpp.git
cd whisper.cpp
make

# Download a model (example: large-v3)
bash ./models/download-ggml-model.sh large-v3

# Configure in .env
echo "TRANSCRIPTION_SERVICE=whisper_cpp" >> .env
echo "WHISPER_CPP_MODEL_PATH=/path/to/whisper.cpp/models/ggml-large-v3.bin" >> .env
```

#### Option C: AWS Transcribe (Cloud)
```bash
# AWS credentials already included in requirements.txt (boto3)
# Configure in .env
echo "TRANSCRIPTION_SERVICE=aws_transcribe" >> .env
echo "AWS_ACCESS_KEY_ID=your_access_key" >> .env
echo "AWS_SECRET_ACCESS_KEY=your_secret_key" >> .env
echo "AWS_REGION=us-east-1" >> .env
```

### 3. Run the Application

```bash
python main.py
```

The API will be available at `http://localhost:8000`

## 🔧 Advanced Configuration

### Environment Variables

Create a `.env` file with your preferred settings:

```env
# OpenAI API Configuration (for AI summaries)
OPENAI_API_KEY=your_openai_api_key_here

# AWS Configuration (for AWS Transcribe)
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1

# Database Configuration
DATABASE_URL=sqlite:///./meeting_analysis.db

# File Upload Settings
MAX_FILE_SIZE_MB=500
ALLOWED_VIDEO_EXTENSIONS=mp4,mov,avi,mkv

# Transcription Settings
TRANSCRIPTION_SERVICE=whisper  # whisper, whisper_cpp, aws_transcribe
WHISPER_MODEL=large-v3
WHISPER_CPP_MODEL_PATH=/path/to/whisper.cpp/models/ggml-large-v3.bin

# Frame Analysis Settings
SCENE_DETECTION_THRESHOLD=27.0
FRAME_EXTRACTION_INTERVAL=10
BLACK_SCREEN_THRESHOLD=15.0
```

### Performance Tuning

#### For High Accuracy
```env
TRANSCRIPTION_SERVICE=whisper
WHISPER_MODEL=large-v3
SCENE_DETECTION_THRESHOLD=20.0
BLACK_SCREEN_THRESHOLD=10.0
```

#### For Speed
```env
TRANSCRIPTION_SERVICE=whisper_cpp
WHISPER_MODEL=base
SCENE_DETECTION_THRESHOLD=30.0
BLACK_SCREEN_THRESHOLD=20.0
```

#### For Cloud Processing
```env
TRANSCRIPTION_SERVICE=aws_transcribe
# AWS credentials required
```

## 🧪 Testing Installation

Run the test script to verify everything works:

```bash
python test_improvements.py
```

Expected output:
```
✓ Configuration loaded correctly
✓ Whisper transcription manager initialized successfully
✓ Black screen detection working correctly
✓ Enhanced frame classification working correctly
✅ All tests completed!
```

## 📋 System Requirements

### Minimum Requirements
- Python 3.8+
- 8GB RAM
- 2GB free disk space
- FFmpeg installed

### Recommended for Production
- Python 3.10+
- 16GB RAM
- SSD storage
- Multi-core CPU
- GPU (for faster Whisper processing)

### External Dependencies

#### FFmpeg
```bash
# macOS
brew install ffmpeg

# Ubuntu/Debian
sudo apt update
sudo apt install ffmpeg

# Windows
# Download from https://ffmpeg.org/download.html
```

#### Whisper.cpp (Optional)
```bash
# Clone and build
git clone https://github.com/ggerganov/whisper.cpp.git
cd whisper.cpp
make

# Download models
bash ./models/download-ggml-model.sh base
bash ./models/download-ggml-model.sh large-v3
```

## 🔍 Troubleshooting

### Common Issues

#### "FFmpeg not found"
```bash
# Verify FFmpeg installation
ffmpeg -version

# Add to PATH if needed (Windows)
set PATH=%PATH%;C:\path\to\ffmpeg\bin
```

#### "Whisper.cpp model not found"
```bash
# Verify model path
ls -la /path/to/whisper.cpp/models/

# Download missing model
cd whisper.cpp
bash ./models/download-ggml-model.sh large-v3
```

#### "AWS credentials not configured"
```bash
# Verify AWS credentials
aws configure list

# Or set in .env file
echo "AWS_ACCESS_KEY_ID=your_key" >> .env
echo "AWS_SECRET_ACCESS_KEY=your_secret" >> .env
```

#### "Out of memory during transcription"
```bash
# Use smaller model
echo "WHISPER_MODEL=base" >> .env

# Or use Whisper.cpp
echo "TRANSCRIPTION_SERVICE=whisper_cpp" >> .env
```

### Performance Issues

#### Slow transcription
- Use `whisper_cpp` for faster CPU processing
- Use smaller Whisper models (`base` instead of `large-v3`)
- Consider AWS Transcribe for cloud processing

#### Poor frame quality
- Adjust `BLACK_SCREEN_THRESHOLD` (lower = more filtering)
- Adjust `SCENE_DETECTION_THRESHOLD` (lower = more sensitive)

#### High memory usage
- Use `whisper_cpp` instead of Python Whisper
- Process shorter video segments
- Use smaller Whisper models

## 🚀 Production Deployment

### Docker Deployment (Recommended)
```dockerfile
FROM python:3.10-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Copy application
COPY . /app
WORKDIR /app

# Install Python dependencies
RUN pip install -r requirements.txt

# Run application
CMD ["python", "main.py"]
```

### Environment-Specific Settings

#### Development
```env
WHISPER_MODEL=base
SCENE_DETECTION_THRESHOLD=30.0
MAX_FILE_SIZE_MB=100
```

#### Production
```env
WHISPER_MODEL=large-v3
SCENE_DETECTION_THRESHOLD=27.0
MAX_FILE_SIZE_MB=500
```

## 📞 Support

If you encounter issues:

1. Check the logs for detailed error messages
2. Run `python test_improvements.py` to verify installation
3. Ensure all dependencies are properly installed
4. Check the troubleshooting section above

For additional support, please refer to the project documentation or create an issue in the repository.
