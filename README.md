# Meeting Transcription & Analysis API

A comprehensive MP4 meeting transcription and analysis system that provides:

- **Multi-language speech-to-text** (EN/RU/UKR) using OpenAI Whisper, Whisper.cpp, or AWS Transcribe
- **Intelligent frame extraction** with scene change detection
- **Timeline synchronization** of audio and visual content
- **AI-powered meeting summarization** using GPT-4
- **RESTful API** for easy integration

## Features

### Core Capabilities
- Upload MP4 video files via REST API
- Extract high-quality audio optimized for transcription
- **Multiple transcription services**: OpenAI Whisper, Whisper.cpp, AWS Transcribe
- Transcribe speech with word-level timestamps and confidence scores
- **Smart frame extraction** with black screen detection and content analysis
- Detect scene changes and extract key presentation frames
- Generate synchronized timeline combining speech and visual events
- Create AI-powered meeting summaries with key points and action items
- Export transcriptions in JSON, SRT, and WebVTT formats

### Supported Languages
- English (en)
- Russian (ru)
- Ukrainian (uk)

### Transcription Services

#### OpenAI Whisper (Default)
- **Best accuracy** for most languages
- Runs locally, no external API calls
- Supports all model sizes (base, medium, large-v3)
- Word-level timestamps and confidence scores

#### Whisper.cpp
- **Fastest performance** on CPU
- Lower memory usage than Python Whisper
- Requires pre-downloaded GGML model files
- Ideal for production deployments

#### AWS Transcribe
- **Cloud-based** transcription service
- Automatic language detection
- Speaker diarization support
- Pay-per-use pricing model

## Installation

### Prerequisites
- Python 3.8+
- FFmpeg installed and accessible in PATH
- OpenAI API key (for AI summarization)

### Quick Start

1. **Clone and setup**
```bash
git clone <repository-url>
cd sim-presentation-analisys
pip install -r requirements.txt
```

2. **Configure environment**
```bash
cp .env.example .env
# Edit .env and add your OpenAI API key
```

3. **Run the application**
```bash
python main.py
```

The API will be available at `http://localhost:8000`

### Environment Configuration

Create a `.env` file with the following settings:

```env
# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# AWS Configuration (for AWS Transcribe)
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1

# Database Configuration
DATABASE_URL=sqlite:///./meeting_analysis.db

# File Upload Settings
MAX_FILE_SIZE_MB=500
ALLOWED_VIDEO_EXTENSIONS=mp4,mov,avi,mkv

# Transcription Settings
TRANSCRIPTION_SERVICE=whisper  # Options: whisper, whisper_cpp, aws_transcribe
WHISPER_MODEL=large-v3
WHISPER_CPP_MODEL_PATH=/path/to/whisper.cpp/models/ggml-large-v3.bin

# Frame Analysis Settings
SCENE_DETECTION_THRESHOLD=27.0
FRAME_EXTRACTION_INTERVAL=10
BLACK_SCREEN_THRESHOLD=15.0
```

## API Usage

### Upload Video
```bash
# Basic upload (auto-detect language)
curl -X POST "http://localhost:8000/api/v1/upload" \
  -F "file=@meeting.mp4"

# Force specific language (for better Russian/Ukrainian transcription)
curl -X POST "http://localhost:8000/api/v1/upload" \
  -F "file=@meeting.mp4" \
  -F "language=ru"
```

Response:
```json
{
  "session_id": 1,
  "message": "Video uploaded successfully. Processing started.",
  "status": "processing"
}
```

### Check Processing Status
```bash
curl "http://localhost:8000/api/v1/sessions/1/status"
```

### Get Results

**Transcription:**
```bash
curl "http://localhost:8000/api/v1/sessions/1/transcription"
```

**Extracted Frames:**
```bash
curl "http://localhost:8000/api/v1/sessions/1/frames"
```

**Synchronized Timeline:**
```bash
curl "http://localhost:8000/api/v1/sessions/1/timeline"
```

**AI Summary:**
```bash
curl "http://localhost:8000/api/v1/sessions/1/summary"
```

### Export Formats

**Beautiful Markdown Report:**
```bash
curl "http://localhost:8000/api/v1/sessions/1/export/markdown" > report.md
```

**Rich HTML Report:**
```bash
curl "http://localhost:8000/api/v1/sessions/1/export/html" > report.html
```

**PDF-Ready HTML:**
```bash
curl "http://localhost:8000/api/v1/sessions/1/export/pdf" > report_pdf.html
```

**SRT Subtitles:**
```bash
curl "http://localhost:8000/api/v1/sessions/1/export/srt" > subtitles.srt
```

**WebVTT:**
```bash
curl "http://localhost:8000/api/v1/sessions/1/export/vtt" > subtitles.vtt
```

**JSON Data:**
```bash
curl "http://localhost:8000/api/v1/sessions/1/export/json" > data.json
```

## Processing Pipeline

The system processes videos through these stages:

1. **Audio Extraction** - Extract high-quality audio (16kHz mono WAV) optimized for Whisper
2. **Speech Recognition** - Multi-language transcription with word-level timestamps
3. **Scene Analysis** - Detect scene changes and extract representative frames
4. **Timeline Sync** - Combine transcription and visual events into unified timeline
5. **AI Summarization** - Generate meeting summary with GPT-4 (optional)

### Expected Processing Times
- **1-hour meeting**: 5-10 minutes total processing
- **Transcription**: 2-4 minutes (Whisper large-v3)
- **Frame Analysis**: 1-2 minutes
- **AI Summary**: 1-2 minutes
- **Timeline Sync**: <30 seconds

## Technical Architecture

### Key Components

- **FastAPI** - REST API framework with async support
- **SQLite** - Database for session management and results storage
- **Multiple Transcription Engines**:
  - **OpenAI Whisper** - State-of-the-art speech recognition
  - **Whisper.cpp** - High-performance C++ implementation
  - **AWS Transcribe** - Cloud-based transcription service
- **PySceneDetect** - Video scene change detection
- **OpenCV** - Computer vision and frame processing
- **FFmpeg** - High-performance video/audio processing
- **Smart Frame Analysis** - Black screen detection and content classification

### Database Schema

- `sessions` - Video upload sessions and metadata
- `transcription_segments` - Word-level transcription with timestamps
- `extracted_frames` - Visual analysis results with scene scores
- `timeline_events` - Synchronized audio/visual events
- `summaries` - AI-generated meeting analysis

## Configuration Options

### Transcription Service Selection
Choose the best transcription service for your needs:
```env
TRANSCRIPTION_SERVICE=whisper        # Default, best accuracy
TRANSCRIPTION_SERVICE=whisper_cpp    # Fastest performance
TRANSCRIPTION_SERVICE=aws_transcribe # Cloud-based
```

### Whisper Models
Choose transcription accuracy vs. speed:
- `large-v3` - Highest accuracy (default)
- `medium` - Balanced performance
- `base` - Fastest processing

### Scene Detection
Adjust sensitivity for different content types:
- `SCENE_DETECTION_THRESHOLD=27.0` - Standard sensitivity
- Lower values = more sensitive to changes
- Higher values = only major scene changes

### Frame Extraction
- `FRAME_EXTRACTION_INTERVAL=10` - Extract frame every N seconds (fallback)
- `BLACK_SCREEN_THRESHOLD=15.0` - Skip frames with mean intensity below this value
- Scene-based extraction takes priority when available

### Black Screen Detection
Automatically filters out black screens and transitions:
- Configurable intensity threshold
- Improves frame quality and reduces noise
- Works with both scene-based and interval extraction

## File Organization

```
sim-presentation-analisys/
├── main.py                 # FastAPI application
├── config.py              # Configuration management
├── database.py            # SQLAlchemy models and database setup
├── models.py              # Pydantic response models
├── utils.py               # Utility functions
├── video_processor.py     # Main processing orchestrator
├── processors/
│   ├── audio_extractor.py # FFmpeg audio extraction
│   ├── transcription.py   # Whisper transcription
│   ├── frame_analyzer.py  # Scene detection and frame extraction
│   ├── timeline_sync.py   # Timeline synchronization
│   └── ai_summarizer.py   # GPT-4 summarization
├── uploads/               # Temporary video storage
├── audio/                 # Extracted audio files
├── frames/                # Extracted video frames
└── requirements.txt       # Python dependencies
```

## API Documentation

Once running, visit `http://localhost:8000/docs` for interactive API documentation with Swagger UI.

## Performance Optimization

### For Large Files
- Increase `MAX_FILE_SIZE_MB` if needed
- Consider using faster Whisper models for quicker processing
- Adjust frame extraction intervals based on content type

### Hardware Recommendations
- **CPU**: Multi-core processor (transcription is CPU-intensive)
- **RAM**: 8GB+ (Whisper models require significant memory)
- **Storage**: SSD recommended for faster I/O during processing

## Troubleshooting

### Common Issues

**FFmpeg not found:**
```bash
# Install FFmpeg
# macOS: brew install ffmpeg
# Ubuntu: sudo apt install ffmpeg
# Windows: Download from https://ffmpeg.org/
```

**Out of memory during transcription:**
- Use smaller Whisper model (`medium` or `base`)
- Process shorter video segments
- Increase system RAM

**Scene detection not working:**
- Adjust `SCENE_DETECTION_THRESHOLD`
- Check video has sufficient visual changes
- Verify video format compatibility

### Logs
Check application logs for detailed error information:
```bash
python main.py > app.log 2>&1
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

MIT License - see LICENSE file for details.