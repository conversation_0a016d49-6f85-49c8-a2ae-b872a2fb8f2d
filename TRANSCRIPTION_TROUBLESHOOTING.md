# Transcription Troubleshooting Guide

This guide addresses common transcription issues, especially the "single phrase" problem you're experiencing.

## 🚨 Common Issue: Only Getting Single Phrases

### Problem Description
- WAV files are transcribed but only return one or two phrases instead of the full content
- Long audio files are not properly segmented
- Missing most of the spoken content

### Root Causes
1. **Python Whisper limitations** with longer audio files
2. **whisper-timestamped library issues** with certain audio formats
3. **Memory constraints** causing truncation
4. **Audio preprocessing problems**

### ✅ Solutions (In Order of Effectiveness)

## Solution 1: Switch to whisper.cpp (Recommended)

whisper.cpp is much more reliable for longer audio files and handles segmentation better.

### Quick Setup
```bash
# Run the automated setup script
./setup_whisper_cpp.sh
```

### Manual Setup
```bash
# Clone and build whisper.cpp
git clone https://github.com/ggerganov/whisper.cpp.git
cd whisper.cpp
make

# Download a model
bash ./models/download-ggml-model.sh medium

# Update .env
echo "TRANSCRIPTION_SERVICE=whisper_cpp" >> .env
echo "WHISPER_CPP_MODEL_PATH=/path/to/whisper.cpp/models/ggml-medium.bin" >> .env
```

## Solution 2: Improved Python Whisper with Chunking

If you prefer to stick with Python Whisper, the enhanced implementation now includes:

### Features
- **Automatic chunking** for files longer than 5 minutes
- **Overlap handling** to prevent missing content
- **Better segmentation** parameters
- **Fallback mechanisms** for problematic files

### Configuration
```env
TRANSCRIPTION_SERVICE=whisper
WHISPER_MODEL=medium  # or large-v3 for best quality
```

## Solution 3: AWS Transcribe (Cloud Alternative)

For cloud-based processing with excellent segmentation:

```env
TRANSCRIPTION_SERVICE=aws_transcribe
AWS_ACCESS_KEY_ID=your_key
AWS_SECRET_ACCESS_KEY=your_secret
AWS_REGION=us-east-1
```

## 🔧 Troubleshooting Steps

### Step 1: Verify Audio File
```bash
# Check audio file properties
ffprobe your_audio.wav

# Expected output should show:
# - Duration: actual length of audio
# - Sample rate: preferably 16000 Hz
# - Channels: 1 (mono) or 2 (stereo)
```

### Step 2: Test with Different Services

```bash
# Test with whisper.cpp
export TRANSCRIPTION_SERVICE=whisper_cpp
python main.py

# Test with chunked Python Whisper
export TRANSCRIPTION_SERVICE=whisper
python main.py

# Test with AWS Transcribe (if configured)
export TRANSCRIPTION_SERVICE=aws_transcribe
python main.py
```

### Step 3: Check Logs

Look for these indicators in the logs:

**Good signs:**
```
INFO: Audio duration: 1234.56 seconds
INFO: Using chunked transcription for long audio file
INFO: Processing chunk 1: 0.0s - 240.0s
INFO: Processing chunk 2: 210.0s - 450.0s
INFO: Transcription completed: 45 segments
```

**Bad signs:**
```
WARNING: Advanced transcription failed, trying basic mode
ERROR: Transcription failed: ...
INFO: Transcription completed: 1 segments  # Only 1 segment for long audio
```

### Step 4: Audio Preprocessing

If issues persist, try preprocessing the audio:

```bash
# Convert to optimal format
ffmpeg -i input.mp4 -ar 16000 -ac 1 -c:a pcm_s16le output.wav

# Normalize audio levels
ffmpeg -i input.wav -af "loudnorm" output_normalized.wav
```

## 🎯 Service Comparison

| Service | Best For | Pros | Cons |
|---------|----------|------|------|
| **whisper.cpp** | Production use | Fast, reliable segmentation, low memory | Requires compilation |
| **Python Whisper** | Development | Easy setup, good for short files | Memory issues with long files |
| **AWS Transcribe** | Cloud deployment | Excellent accuracy, no local resources | Requires AWS account, costs money |

## 🔍 Debugging Commands

### Test whisper.cpp directly
```bash
# Test whisper.cpp with your audio file
./whisper.cpp/main -m ./whisper.cpp/models/ggml-medium.bin -f your_audio.wav -osrt

# Check if it produces multiple segments
cat your_audio.wav.srt
```

### Test Python Whisper directly
```python
import whisper
model = whisper.load_model("medium")
result = model.transcribe("your_audio.wav", condition_on_previous_text=False)
print(f"Segments: {len(result['segments'])}")
for i, segment in enumerate(result['segments'][:5]):
    print(f"{i}: {segment['start']:.2f}s - {segment['end']:.2f}s: {segment['text']}")
```

### Check transcription manager
```python
from processors.transcription_manager import TranscriptionManager

manager = TranscriptionManager(service="whisper_cpp", 
                              whisper_cpp_model_path="/path/to/model.bin")
segments, language = manager.transcribe_audio("your_audio.wav")
print(f"Got {len(segments)} segments")
```

## 🚀 Performance Optimization

### For Speed
```env
TRANSCRIPTION_SERVICE=whisper_cpp
WHISPER_CPP_MODEL_PATH=/path/to/ggml-base.bin  # Fastest model
```

### For Accuracy
```env
TRANSCRIPTION_SERVICE=whisper_cpp
WHISPER_CPP_MODEL_PATH=/path/to/ggml-large-v3.bin  # Best quality
```

### For Balance
```env
TRANSCRIPTION_SERVICE=whisper_cpp
WHISPER_CPP_MODEL_PATH=/path/to/ggml-medium.bin  # Recommended
```

## 📋 Checklist for Single Phrase Issue

- [ ] Audio file is longer than expected single phrase
- [ ] Audio file plays correctly in media player
- [ ] Using whisper.cpp instead of Python Whisper
- [ ] Model file exists and is not corrupted
- [ ] Sufficient disk space for processing
- [ ] Logs show multiple segments being processed
- [ ] Test with a known working audio file

## 🆘 Still Having Issues?

### Quick Test
```bash
# Run the improved test script
python test_improvements.py

# Test with a sample audio file
curl -X POST "http://localhost:8000/api/v1/upload" \
  -F "file=@test_audio.wav" \
  -F "language=en"

# Check the results
curl "http://localhost:8000/api/v1/sessions/1/transcription"
```

### Get Detailed Logs
```bash
# Run with debug logging
export PYTHONPATH=.
python -c "
import logging
logging.basicConfig(level=logging.DEBUG)
from processors.transcription_manager import TranscriptionManager
manager = TranscriptionManager(service='whisper_cpp')
segments, lang = manager.transcribe_audio('your_audio.wav')
print(f'Result: {len(segments)} segments')
"
```

### Common Solutions Summary

1. **Use whisper.cpp** - Most reliable for long audio files
2. **Check audio format** - Convert to 16kHz mono WAV if needed
3. **Verify model files** - Ensure whisper.cpp models are downloaded
4. **Monitor memory usage** - Large models need sufficient RAM
5. **Check file permissions** - Ensure whisper.cpp executable has proper permissions

The enhanced system should now handle your WAV files much better, especially with whisper.cpp as the default transcription service.
