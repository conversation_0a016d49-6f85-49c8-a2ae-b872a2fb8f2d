#!/bin/bash

# Setup script for whisper.cpp
# This script downloads, compiles, and configures whisper.cpp for the meeting analysis system

set -e

echo "🚀 Setting up whisper.cpp for Meeting Analysis System"
echo "=================================================="

# Check if we're in the right directory
if [ ! -f "main.py" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

# Create whisper.cpp directory
WHISPER_DIR="whisper.cpp"
MODEL_DIR="$WHISPER_DIR/models"

echo "📁 Creating whisper.cpp directory..."
if [ -d "$WHISPER_DIR" ]; then
    echo "⚠️  whisper.cpp directory already exists. Updating..."
    cd "$WHISPER_DIR"
    git pull
    cd ..
else
    echo "📥 Cloning whisper.cpp repository..."
    git clone https://github.com/ggerganov/whisper.cpp.git
fi

# Build whisper.cpp
echo "🔨 Building whisper.cpp..."
cd "$WHISPER_DIR"

# Check if make is available
if ! command -v make &> /dev/null; then
    echo "❌ Error: 'make' is not installed. Please install build tools:"
    echo "   Ubuntu/Debian: sudo apt install build-essential"
    echo "   macOS: xcode-select --install"
    echo "   Windows: Install Visual Studio or MinGW"
    exit 1
fi

# Build with optimizations
make clean
make -j$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)

if [ ! -f "main" ]; then
    echo "❌ Error: Failed to build whisper.cpp"
    exit 1
fi

echo "✅ whisper.cpp built successfully"

# Download models
echo "📥 Downloading whisper models..."

# Create models directory if it doesn't exist
mkdir -p models

# Download base model (fastest, good for testing)
if [ ! -f "models/ggml-base.bin" ]; then
    echo "📥 Downloading base model (fastest)..."
    bash ./models/download-ggml-model.sh base
fi

# Download medium model (balanced)
if [ ! -f "models/ggml-medium.bin" ]; then
    echo "📥 Downloading medium model (balanced)..."
    bash ./models/download-ggml-model.sh medium
fi

# Download large-v3 model (best quality)
echo "📥 Downloading large-v3 model (best quality)..."
echo "⚠️  This is a large download (~1.5GB). Continue? (y/N)"
read -r response
if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    if [ ! -f "models/ggml-large-v3.bin" ]; then
        bash ./models/download-ggml-model.sh large-v3
    fi
else
    echo "⏭️  Skipping large-v3 model download"
fi

cd ..

# Update .env file
echo "⚙️  Configuring environment..."

ENV_FILE=".env"
if [ ! -f "$ENV_FILE" ]; then
    cp .env.example "$ENV_FILE"
    echo "📄 Created .env file from template"
fi

# Get absolute path to whisper.cpp
WHISPER_CPP_PATH=$(realpath "$WHISPER_DIR/main")
MODEL_PATH=$(realpath "$MODEL_DIR/ggml-medium.bin")

# Check if large-v3 model exists and use it instead
if [ -f "$MODEL_DIR/ggml-large-v3.bin" ]; then
    MODEL_PATH=$(realpath "$MODEL_DIR/ggml-large-v3.bin")
    echo "🎯 Using large-v3 model for best quality"
else
    echo "🎯 Using medium model for balanced performance"
fi

# Update .env file
echo "📝 Updating .env configuration..."

# Remove existing whisper.cpp configuration
sed -i.bak '/TRANSCRIPTION_SERVICE=whisper_cpp/d' "$ENV_FILE" 2>/dev/null || true
sed -i.bak '/WHISPER_CPP_MODEL_PATH=/d' "$ENV_FILE" 2>/dev/null || true

# Add new configuration
echo "" >> "$ENV_FILE"
echo "# Whisper.cpp Configuration (auto-generated)" >> "$ENV_FILE"
echo "TRANSCRIPTION_SERVICE=whisper_cpp" >> "$ENV_FILE"
echo "WHISPER_CPP_MODEL_PATH=$MODEL_PATH" >> "$ENV_FILE"

echo "✅ Configuration updated in $ENV_FILE"

# Test whisper.cpp installation
echo "🧪 Testing whisper.cpp installation..."

# Create a test audio file (1 second of silence)
TEST_AUDIO="test_audio.wav"
ffmpeg -f lavfi -i "anullsrc=channel_layout=mono:sample_rate=16000" -t 1 -y "$TEST_AUDIO" 2>/dev/null

if [ -f "$TEST_AUDIO" ]; then
    echo "🎵 Testing transcription with whisper.cpp..."
    
    # Test whisper.cpp
    if "$WHISPER_CPP_PATH" -m "$MODEL_PATH" -f "$TEST_AUDIO" -nt > /dev/null 2>&1; then
        echo "✅ whisper.cpp test successful!"
    else
        echo "⚠️  whisper.cpp test failed, but installation appears complete"
    fi
    
    # Clean up test file
    rm -f "$TEST_AUDIO"
else
    echo "⚠️  Could not create test audio (ffmpeg not available), skipping test"
fi

# Test Python integration
echo "🐍 Testing Python integration..."
python3 -c "
import sys
sys.path.append('.')
from processors.whisper_cpp import WhisperCppTranscriber
try:
    transcriber = WhisperCppTranscriber('$MODEL_PATH')
    print('✅ Python integration test successful!')
except Exception as e:
    print(f'⚠️  Python integration test failed: {e}')
" 2>/dev/null || echo "⚠️  Python integration test failed"

echo ""
echo "🎉 whisper.cpp setup complete!"
echo "=================================================="
echo ""
echo "📋 Summary:"
echo "   • whisper.cpp installed in: $WHISPER_DIR"
echo "   • Executable: $WHISPER_CPP_PATH"
echo "   • Model: $MODEL_PATH"
echo "   • Configuration updated in: $ENV_FILE"
echo ""
echo "🚀 You can now start the meeting analysis system:"
echo "   python main.py"
echo ""
echo "💡 Tips:"
echo "   • For faster processing: Use base model (edit WHISPER_CPP_MODEL_PATH in .env)"
echo "   • For best quality: Use large-v3 model"
echo "   • For balanced performance: Use medium model (current default)"
echo ""
echo "🔧 Available models:"
ls -la "$MODEL_DIR"/*.bin 2>/dev/null | awk '{print "   • " $9}' || echo "   • No models found"
echo ""
echo "✅ Setup complete! Your system should now handle long audio files much better."
