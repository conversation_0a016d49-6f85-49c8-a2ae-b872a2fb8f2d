#!/usr/bin/env python3
"""
Quick fix script for the single phrase transcription issue.
This script will:
1. Detect the current transcription problem
2. Apply the best fix based on your system
3. Test the fix with a sample
"""

import os
import sys
import logging
import subprocess
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from config import settings

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def check_current_setup():
    """Check the current transcription setup"""
    logger.info("🔍 Checking current transcription setup...")
    
    issues = []
    recommendations = []
    
    # Check current service
    logger.info(f"Current transcription service: {settings.TRANSCRIPTION_SERVICE}")
    
    if settings.TRANSCRIPTION_SERVICE == "whisper":
        logger.info("⚠️  Using Python Whisper - this can cause single phrase issues with long audio")
        issues.append("Python Whisper single phrase issue")
        recommendations.append("Switch to whisper.cpp for better segmentation")
    
    elif settings.TRANSCRIPTION_SERVICE == "whisper_cpp":
        if not settings.WHISPER_CPP_MODEL_PATH:
            logger.error("❌ Whisper.cpp selected but no model path configured")
            issues.append("Missing whisper.cpp model path")
            recommendations.append("Configure WHISPER_CPP_MODEL_PATH or run setup_whisper_cpp.sh")
        elif not Path(settings.WHISPER_CPP_MODEL_PATH).exists():
            logger.error(f"❌ Whisper.cpp model not found: {settings.WHISPER_CPP_MODEL_PATH}")
            issues.append("Whisper.cpp model file missing")
            recommendations.append("Download whisper.cpp model or run setup_whisper_cpp.sh")
        else:
            logger.info("✅ Whisper.cpp configured correctly")
    
    elif settings.TRANSCRIPTION_SERVICE == "aws_transcribe":
        if not (settings.AWS_ACCESS_KEY_ID and settings.AWS_SECRET_ACCESS_KEY):
            logger.error("❌ AWS Transcribe selected but credentials not configured")
            issues.append("Missing AWS credentials")
            recommendations.append("Configure AWS credentials in .env file")
        else:
            logger.info("✅ AWS Transcribe configured")
    
    return issues, recommendations

def apply_quick_fix():
    """Apply the quickest fix for the transcription issue"""
    logger.info("🔧 Applying quick fix...")
    
    # Strategy 1: Try to use whisper.cpp if available
    whisper_cpp_dir = Path("whisper.cpp")
    if whisper_cpp_dir.exists() and (whisper_cpp_dir / "main").exists():
        logger.info("✅ Found existing whisper.cpp installation")
        
        # Find a model
        models_dir = whisper_cpp_dir / "models"
        model_files = list(models_dir.glob("ggml-*.bin")) if models_dir.exists() else []
        
        if model_files:
            # Use the best available model
            model_priority = ["large-v3", "medium", "base", "small", "tiny"]
            selected_model = None
            
            for priority in model_priority:
                for model_file in model_files:
                    if priority in model_file.name:
                        selected_model = model_file
                        break
                if selected_model:
                    break
            
            if not selected_model:
                selected_model = model_files[0]  # Use any available model
            
            logger.info(f"✅ Using model: {selected_model}")
            
            # Update .env file
            update_env_file("whisper_cpp", str(selected_model.absolute()))
            return True
        else:
            logger.warning("⚠️  whisper.cpp found but no models available")
    
    # Strategy 2: Improve Python Whisper configuration
    logger.info("🔧 Configuring improved Python Whisper with chunking")
    update_env_file("whisper", None)
    
    return True

def update_env_file(service, model_path=None):
    """Update .env file with new transcription settings"""
    env_file = Path(".env")
    
    if not env_file.exists():
        # Create from template
        template_file = Path(".env.example")
        if template_file.exists():
            import shutil
            shutil.copy(template_file, env_file)
            logger.info("📄 Created .env file from template")
        else:
            # Create minimal .env
            with open(env_file, 'w') as f:
                f.write("# Meeting Analysis Configuration\n")
            logger.info("📄 Created new .env file")
    
    # Read current content
    with open(env_file, 'r') as f:
        lines = f.readlines()
    
    # Remove existing transcription settings
    lines = [line for line in lines if not line.startswith('TRANSCRIPTION_SERVICE=')]
    lines = [line for line in lines if not line.startswith('WHISPER_CPP_MODEL_PATH=')]
    
    # Add new settings
    lines.append(f"\n# Transcription fix applied by fix_transcription_issue.py\n")
    lines.append(f"TRANSCRIPTION_SERVICE={service}\n")
    
    if model_path:
        lines.append(f"WHISPER_CPP_MODEL_PATH={model_path}\n")
    
    # Write back
    with open(env_file, 'w') as f:
        f.writelines(lines)
    
    logger.info(f"✅ Updated .env file with TRANSCRIPTION_SERVICE={service}")

def test_fix():
    """Test the applied fix"""
    logger.info("🧪 Testing the fix...")
    
    try:
        # Import after potential .env changes
        import importlib
        import config
        importlib.reload(config)
        
        from processors.transcription_manager import TranscriptionManager
        
        # Test the configured service
        manager = TranscriptionManager(
            service=config.settings.TRANSCRIPTION_SERVICE,
            whisper_model="base",
            whisper_cpp_model_path=config.settings.WHISPER_CPP_MODEL_PATH
        )
        
        logger.info(f"✅ Successfully initialized {config.settings.TRANSCRIPTION_SERVICE} transcription manager")
        
        # Test supported languages
        languages = manager.get_supported_languages()
        logger.info(f"✅ Supported languages: {languages}")
        
        # Check for chunking support (helps with long audio)
        transcriber = manager.get_transcriber()
        if hasattr(transcriber, '_transcribe_chunked'):
            logger.info("✅ Chunked transcription support available (prevents single phrase issue)")
        elif hasattr(transcriber, '_process_srt_output'):
            logger.info("✅ SRT output processing available (good segmentation)")
        
        manager.cleanup()
        
        logger.info("✅ Fix test successful!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Fix test failed: {e}")
        return False

def provide_recommendations():
    """Provide additional recommendations"""
    logger.info("💡 Additional recommendations:")
    
    print("\n📋 To completely solve the single phrase issue:")
    print("1. 🚀 Best solution: Install whisper.cpp")
    print("   ./setup_whisper_cpp.sh")
    print("")
    print("2. 🔧 Alternative: The improved Python Whisper now includes:")
    print("   • Automatic chunking for long files (>5 minutes)")
    print("   • Better segmentation parameters")
    print("   • Overlap handling to prevent missing content")
    print("")
    print("3. ☁️  Cloud option: Use AWS Transcribe")
    print("   • Set AWS credentials in .env file")
    print("   • Set TRANSCRIPTION_SERVICE=aws_transcribe")
    print("")
    print("🎯 For immediate testing:")
    print("   python main.py")
    print("   # Upload a long audio file and check if you get multiple segments")
    print("")
    print("🔍 To debug transcription issues:")
    print("   python test_improvements.py")

def main():
    """Main fix application"""
    print("🚀 Meeting Analysis Transcription Fix")
    print("=" * 50)
    
    # Check current setup
    issues, recommendations = check_current_setup()
    
    if not issues:
        logger.info("✅ No transcription issues detected!")
        return
    
    print(f"\n❌ Found {len(issues)} issue(s):")
    for issue in issues:
        print(f"   • {issue}")
    
    print(f"\n💡 Recommendations:")
    for rec in recommendations:
        print(f"   • {rec}")
    
    # Ask user if they want to apply the fix
    print(f"\n🔧 Apply automatic fix? (y/N): ", end="")
    response = input().strip().lower()
    
    if response in ['y', 'yes']:
        if apply_quick_fix():
            print("\n✅ Fix applied successfully!")
            
            # Test the fix
            if test_fix():
                print("\n🎉 Transcription system is now configured to handle long audio files better!")
            else:
                print("\n⚠️  Fix applied but testing failed. Check the logs above.")
        else:
            print("\n❌ Failed to apply fix automatically.")
    
    # Always provide recommendations
    provide_recommendations()

if __name__ == "__main__":
    main()
