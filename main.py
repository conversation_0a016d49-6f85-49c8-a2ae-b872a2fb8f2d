from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException, BackgroundTasks, Depends, Form, Path as QueryPath
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import Optional
import shutil
import logging
from pathlib import Path
from datetime import datetime

from config import settings
from database import get_db, Session as DBSession, SessionStatus
from models import UploadResponse, SessionResponse
from utils import validate_video_file, generate_unique_filename, ensure_directory_exists, get_file_size_mb
from video_processor import video_processor

app = FastAPI(
    title="Meeting Transcription & Analysis API",
    description="""
    🎥 **Advanced MP4 Meeting Analysis Platform**

    Transform your meeting recordings into actionable insights with our comprehensive analysis pipeline:

    ## 🚀 Key Features

    ### 🗣️ **Multi-Language Speech Recognition**
    - Support for English, Russian, and Ukrainian
    - Word-level timestamps with confidence scores
    - Advanced language detection with multi-segment analysis
    - Force language option for better accuracy

    ### 🖼️ **Intelligent Visual Analysis**
    - Automatic scene change detection
    - Key frame extraction from presentations
    - Timeline synchronization of audio and visual events
    - Frame classification (slides, speakers, transitions)

    ### 🤖 **AI-Powered Insights**
    - Executive summary generation
    - Key discussion points extraction
    - Action items and decisions identification
    - Meeting statistics and analytics

    ### 📊 **Professional Export Formats**
    - Beautiful HTML reports with modern styling
    - Professional Markdown documentation
    - PDF-ready formatted output
    - SRT/VTT subtitles for video players
    - Complete JSON data export

    ## 🔧 **Processing Pipeline**
    1. **Audio Extraction** - High-quality audio optimized for Whisper
    2. **Speech Recognition** - Multi-language transcription with timestamps
    3. **Scene Analysis** - Intelligent frame extraction and classification
    4. **Timeline Sync** - Unified audio-visual timeline
    5. **AI Analysis** - GPT-4 powered summarization and insights

    ## 🌍 **Language Support**
    - **English** (`en`) - Full support with high accuracy
    - **Russian** (`ru`) - Enhanced detection and transcription
    - **Ukrainian** (`uk`) - Native language support

    ## 📋 **Use Cases**
    - Business meeting transcription and analysis
    - Educational lecture processing
    - Interview analysis and summarization
    - Presentation content extraction
    - Multilingual content processing
    """,
    version="1.0.0",
    contact={
        "name": "Meeting Analysis API",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
    openapi_tags=[
        {
            "name": "Upload & Processing",
            "description": "Upload videos and manage processing sessions"
        },
        {
            "name": "Session Management",
            "description": "Retrieve session information and processing status"
        },
        {
            "name": "Results & Analysis",
            "description": "Access transcription, frames, timeline, and AI analysis"
        },
        {
            "name": "Export & Formats",
            "description": "Export results in various professional formats"
        },
        {
            "name": "System",
            "description": "Health checks and system information"
        }
    ]
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create necessary directories
for directory in [settings.UPLOAD_DIR, settings.FRAMES_DIR, settings.AUDIO_DIR]:
    ensure_directory_exists(directory)

@app.get("/", tags=["System"])
async def root():
    """API root endpoint with basic information"""
    return {"message": "Meeting Transcription & Analysis API"}

@app.get("/health", tags=["System"])
async def health_check():
    """Health check endpoint for monitoring"""
    return {"status": "healthy"}

@app.post("/api/v1/upload", response_model=UploadResponse, tags=["Upload & Processing"])
async def upload_video(
    file: UploadFile = File(..., description="MP4 video file to process"),
    language: Optional[str] = Form(
        None,
        description="Force specific language for transcription (en, ru, uk). If not provided, language will be auto-detected."
    ),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: Session = Depends(get_db)
):
    """Upload MP4 video file for processing with optional language specification

    **Supported Languages:**
    - `en` - English
    - `ru` - Russian
    - `uk` - Ukrainian

    **Processing Pipeline:**
    1. Audio extraction optimized for Whisper
    2. Multi-language speech recognition with word-level timestamps
    3. Scene detection and frame extraction
    4. Timeline synchronization of audio and visual events
    5. AI-powered meeting summarization (requires OpenAI API key)

    **Language Detection:**
    - If language is not specified, the system will auto-detect using multi-segment analysis
    - For Russian content misidentified as English, use `language=ru` parameter
    - Language detection prioritizes Russian/Ukrainian when confidence levels are close
    """

    # Validate file type
    if not validate_video_file(file.filename, settings.ALLOWED_VIDEO_EXTENSIONS):
        raise HTTPException(
            status_code=400,
            detail=f"Invalid file type. Allowed extensions: {', '.join(settings.ALLOWED_VIDEO_EXTENSIONS)}"
        )

    # Validate language parameter
    if language and language not in settings.SUPPORTED_LANGUAGES:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported language '{language}'. Supported languages: {', '.join(settings.SUPPORTED_LANGUAGES)}"
        )

    # Generate unique filename
    unique_filename = generate_unique_filename(file.filename)
    file_path = settings.UPLOAD_DIR / unique_filename

    try:
        # Save uploaded file
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        # Check file size
        file_size_mb = get_file_size_mb(file_path)
        if file_size_mb > settings.MAX_FILE_SIZE_MB:
            file_path.unlink()  # Delete file
            raise HTTPException(
                status_code=413,
                detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE_MB}MB"
            )

        # Create database session
        db_session = DBSession(
            filename=unique_filename,
            original_filename=file.filename,
            status=SessionStatus.PROCESSING
        )
        db.add(db_session)
        db.commit()
        db.refresh(db_session)

        # Add background processing task
        background_tasks.add_task(process_video_task, db_session.id, file_path, language)

        logger.info(f"Video uploaded successfully: {file.filename} -> {unique_filename}")

        return UploadResponse(
            session_id=db_session.id,
            message="Video uploaded successfully. Processing started.",
            status="processing"
        )

    except Exception as e:
        # Clean up file if database operation fails
        if file_path.exists():
            file_path.unlink()
        logger.error(f"Upload failed: {e}")
        raise HTTPException(status_code=500, detail="Upload failed")

@app.get("/api/v1/sessions/{session_id}", response_model=SessionResponse, tags=["Session Management"])
async def get_session(
    session_id: int,
    db: Session = Depends(get_db)
):
    """Get detailed information about a processing session

    Returns session metadata including:
    - Processing status (processing, completed, failed)
    - Original filename and duration
    - Detected language
    - Creation and completion timestamps
    """
    db_session = db.query(DBSession).filter(DBSession.id == session_id).first()
    if not db_session:
        raise HTTPException(status_code=404, detail="Session not found")

    return SessionResponse.from_orm(db_session)

@app.get("/api/v1/sessions", tags=["Session Management"])
async def list_sessions(
    skip: int = 0,
    limit: int = 20,
    db: Session = Depends(get_db)
):
    """List all sessions with pagination"""
    sessions = (
        db.query(DBSession)
        .order_by(DBSession.created_at.desc())
        .offset(skip)
        .limit(limit)
        .all()
    )

    total = db.query(DBSession).count()

    return {
        "sessions": [SessionResponse.from_orm(session) for session in sessions],
        "total": total,
        "skip": skip,
        "limit": limit
    }

@app.get("/api/v1/sessions/{session_id}/transcription", tags=["Results & Analysis"])
async def get_transcription(session_id: int, db: Session = Depends(get_db)):
    """Get transcription segments for a session"""
    from database import TranscriptionSegment

    # Check if session exists
    session = db.query(DBSession).filter(DBSession.id == session_id).first()
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    # Get transcription segments
    segments = (
        db.query(TranscriptionSegment)
        .filter(TranscriptionSegment.session_id == session_id)
        .order_by(TranscriptionSegment.start_time)
        .all()
    )

    return {
        "session_id": session_id,
        "segments": segments,
        "total_segments": len(segments)
    }

@app.get("/api/v1/sessions/{session_id}/frames", tags=["Results & Analysis"])
async def get_frames(session_id: int, db: Session = Depends(get_db)):
    """Get extracted frames for a session"""
    from database import ExtractedFrame

    # Check if session exists
    session = db.query(DBSession).filter(DBSession.id == session_id).first()
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    # Get extracted frames
    frames = (
        db.query(ExtractedFrame)
        .filter(ExtractedFrame.session_id == session_id)
        .order_by(ExtractedFrame.timestamp)
        .all()
    )

    return {
        "session_id": session_id,
        "frames": frames,
        "total_frames": len(frames)
    }

@app.get("/api/v1/sessions/{session_id}/timeline", tags=["Results & Analysis"])
async def get_timeline(session_id: int, db: Session = Depends(get_db)):
    """Get synchronized timeline for a session"""
    from database import TimelineEvent

    # Check if session exists
    session = db.query(DBSession).filter(DBSession.id == session_id).first()
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    # Get timeline events
    events = (
        db.query(TimelineEvent)
        .filter(TimelineEvent.session_id == session_id)
        .order_by(TimelineEvent.timestamp)
        .all()
    )

    return {
        "session_id": session_id,
        "duration": session.duration,
        "events": events,
        "total_events": len(events)
    }

@app.get("/api/v1/sessions/{session_id}/summary", tags=["Results & Analysis"])
async def get_summary(session_id: int, db: Session = Depends(get_db)):
    """Get AI-generated summary for a session"""
    from database import Summary

    # Check if session exists
    session = db.query(DBSession).filter(DBSession.id == session_id).first()
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    # Get summary
    summary = (
        db.query(Summary)
        .filter(Summary.session_id == session_id)
        .first()
    )

    if not summary:
        raise HTTPException(status_code=404, detail="Summary not available yet")

    return {
        "session_id": session_id,
        "summary": summary
    }

@app.get("/api/v1/sessions/{session_id}/export/{format}", tags=["Export & Formats"])
async def export_session(
    session_id: int,
    format: str,
    db: Session = Depends(get_db)
):
    """Export meeting analysis in beautiful, professional formats

    **Available Formats:**

    📄 **Document Formats:**
    - `markdown` or `md` - Professional Markdown report with sections, statistics, and formatting
    - `html` - Beautiful responsive HTML report with modern styling and interactive elements
    - `pdf` - Print-optimized HTML for PDF conversion with proper page breaks

    📹 **Subtitle Formats:**
    - `srt` - SubRip subtitle format for video players
    - `vtt` - WebVTT subtitle format for web video

    📊 **Data Formats:**
    - `json` - Complete structured data including transcription, frames, timeline, and summary

    **Document Features:**
    - Executive summary with key points and action items
    - Visual timeline showing scene changes and key frames
    - Full transcription with confidence indicators and timestamps
    - Meeting statistics (duration, word count, speech rate, etc.)
    - Professional styling suitable for business presentations
    - Responsive design that works on all devices

    **Usage Examples:**
    ```bash
    # Get beautiful HTML report
    curl "http://localhost:8000/api/v1/sessions/1/export/html" > report.html

    # Get markdown for documentation
    curl "http://localhost:8000/api/v1/sessions/1/export/markdown" > meeting_notes.md

    # Get SRT subtitles
    curl "http://localhost:8000/api/v1/sessions/1/export/srt" > subtitles.srt
    ```
    """
    from database import TranscriptionSegment, ExtractedFrame, Summary
    from processors.document_exporter import DocumentExporter
    from fastapi.responses import HTMLResponse, PlainTextResponse

    # Check if session exists
    session = db.query(DBSession).filter(DBSession.id == session_id).first()
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    allowed_formats = ['json', 'srt', 'vtt', 'markdown', 'md', 'html', 'pdf']
    if format.lower() not in allowed_formats:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported format. Use: {', '.join(allowed_formats)}"
        )

    # Get all session data for comprehensive exports
    segments = (
        db.query(TranscriptionSegment)
        .filter(TranscriptionSegment.session_id == session_id)
        .order_by(TranscriptionSegment.start_time)
        .all()
    )

    frames = (
        db.query(ExtractedFrame)
        .filter(ExtractedFrame.session_id == session_id)
        .order_by(ExtractedFrame.timestamp)
        .all()
    )

    # Get timeline events for comprehensive export
    from database import TimelineEvent
    timeline_events = (
        db.query(TimelineEvent)
        .filter(TimelineEvent.session_id == session_id)
        .order_by(TimelineEvent.timestamp)
        .all()
    )

    summary = (
        db.query(Summary)
        .filter(Summary.session_id == session_id)
        .first()
    )

    # Basic formats
    if format.lower() == 'json':
        return {
            "session": SessionResponse.from_orm(session).dict(),
            "transcription_segments": [_segment_to_dict(s) for s in segments],
            "extracted_frames": [_frame_to_dict(f) for f in frames],
            "timeline_events": [_timeline_event_to_dict(e) for e in timeline_events],
            "summary": _summary_to_dict(summary) if summary else None,
            "metadata": {
                "total_segments": len(segments),
                "total_frames": len(frames),
                "total_timeline_events": len(timeline_events),
                "transcription_service": settings.TRANSCRIPTION_SERVICE,
                "export_timestamp": datetime.now().isoformat()
            }
        }
    elif format.lower() == 'srt':
        return PlainTextResponse(_export_srt(segments), media_type="text/plain")
    elif format.lower() == 'vtt':
        return PlainTextResponse(_export_vtt(segments), media_type="text/plain")

    # Rich document formats
    elif format.lower() in ['markdown', 'md']:
        exporter = DocumentExporter(settings.FRAMES_DIR)
        session_data = {
            "session": SessionResponse.from_orm(session).dict(),
            "transcription_segments": [_segment_to_dict(s) for s in segments],
            "extracted_frames": [_frame_to_dict(f) for f in frames],
            "summary": _summary_to_dict(summary) if summary else None
        }
        markdown_content = exporter.export_to_markdown(session_data)
        return PlainTextResponse(
            markdown_content,
            media_type="text/markdown",
            headers={"Content-Disposition": f"attachment; filename=meeting_report_{session_id}.md"}
        )

    elif format.lower() == 'html':
        exporter = DocumentExporter(settings.FRAMES_DIR)
        session_data = {
            "session": SessionResponse.from_orm(session).dict(),
            "transcription_segments": [_segment_to_dict(s) for s in segments],
            "extracted_frames": [_frame_to_dict(f) for f in frames],
            "summary": _summary_to_dict(summary) if summary else None
        }
        html_content = exporter.export_to_html(session_data)
        return HTMLResponse(
            html_content,
            headers={"Content-Disposition": f"attachment; filename=meeting_report_{session_id}.html"}
        )

    elif format.lower() == 'pdf':
        exporter = DocumentExporter(settings.FRAMES_DIR)
        session_data = {
            "session": SessionResponse.from_orm(session).dict(),
            "transcription_segments": [_segment_to_dict(s) for s in segments],
            "extracted_frames": [_frame_to_dict(f) for f in frames],
            "summary": _summary_to_dict(summary) if summary else None
        }
        # Return PDF-optimized HTML for conversion
        pdf_html = exporter.export_to_pdf_html(session_data)
        return HTMLResponse(
            pdf_html,
            headers={
                "Content-Disposition": f"attachment; filename=meeting_report_{session_id}_pdf.html",
                "X-PDF-Ready": "true"
            }
        )

def _segment_to_dict(segment) -> dict:
    """Convert transcription segment to dict"""
    return {
        "start_time": segment.start_time,
        "end_time": segment.end_time,
        "text": segment.text,
        "confidence": segment.confidence,
        "language": segment.language,
        "speaker_id": segment.speaker_id
    }

def _frame_to_dict(frame) -> dict:
    """Convert extracted frame to dict"""
    return {
        "timestamp": frame.timestamp,
        "filename": frame.filename,
        "scene_change_score": frame.scene_change_score,
        "frame_type": frame.frame_type.value if frame.frame_type else None,
        "similarity_to_previous": frame.similarity_to_previous
    }

def _timeline_event_to_dict(event) -> dict:
    """Convert timeline event to dict"""
    return {
        "timestamp": event.timestamp,
        "event_type": event.event_type.value if event.event_type else None,
        "transcription_segment_id": event.transcription_segment_id,
        "frame_id": event.frame_id
    }

def _summary_to_dict(summary) -> dict:
    """Convert summary to dict"""
    if not summary:
        return None

    return {
        "executive_summary": summary.executive_summary,
        "key_points": summary.key_points,
        "action_items": summary.action_items,
        "decisions": summary.decisions,
        "slide_descriptions": summary.slide_descriptions,
        "topic_timeline": summary.topic_timeline,
        "created_at": summary.created_at
    }

def _export_srt(segments) -> str:
    """Export transcription as SRT subtitle format"""
    srt_content = []
    for i, segment in enumerate(segments, 1):
        start_time = _format_srt_time(segment.start_time)
        end_time = _format_srt_time(segment.end_time)

        srt_content.append(f"{i}")
        srt_content.append(f"{start_time} --> {end_time}")
        srt_content.append(segment.text)
        srt_content.append("")

    return "\n".join(srt_content)

def _export_vtt(segments) -> str:
    """Export transcription as WebVTT format"""
    vtt_content = ["WEBVTT", ""]

    for segment in segments:
        start_time = _format_vtt_time(segment.start_time)
        end_time = _format_vtt_time(segment.end_time)

        vtt_content.append(f"{start_time} --> {end_time}")
        vtt_content.append(segment.text)
        vtt_content.append("")

    return "\n".join(vtt_content)

def _format_srt_time(seconds: float) -> str:
    """Format seconds to SRT time format (HH:MM:SS,mmm)"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millis = int((seconds % 1) * 1000)
    return f"{hours:02d}:{minutes:02d}:{secs:02d},{millis:03d}"

def _format_vtt_time(seconds: float) -> str:
    """Format seconds to WebVTT time format (HH:MM:SS.mmm)"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millis = int((seconds % 1) * 1000)
    return f"{hours:02d}:{minutes:02d}:{secs:02d}.{millis:03d}"

def process_video_task(session_id: int, video_path: Path, language: Optional[str] = None):
    """Background task for video processing"""
    try:
        logger.info(f"Starting background processing for session {session_id}")
        success = video_processor.process_video(session_id, video_path, language)

        if success:
            logger.info(f"Background processing completed for session {session_id}")
        else:
            logger.error(f"Background processing failed for session {session_id}")
    except Exception as e:
        logger.error(f"Background processing error for session {session_id}: {e}")
    finally:
        # Clean up uploaded video file
        try:
            if video_path.exists():
                video_path.unlink()
                logger.info(f"Cleaned up uploaded file: {video_path}")
        except Exception as e:
            logger.error(f"Error cleaning up uploaded file: {e}")

@app.get("/api/v1/sessions/{session_id}/status", tags=["Session Management"])
async def get_processing_status(session_id: int):
    """Get detailed processing status and statistics

    Returns comprehensive processing information:
    - Current processing status
    - Processing statistics (segments, frames, events)
    - Duration and language detection results
    - AI summary availability
    - Processing timestamps

    **Status Values:**
    - `processing` - Video is currently being processed
    - `completed` - All processing steps completed successfully
    - `failed` - Processing failed (check logs for details)
    """
    return video_processor.get_processing_status(session_id)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)