
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meeting Analysis Report - PMI + Attentional window - results of the day  (MEETING RECORDING)_20250618_135323.mp4</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header .meta {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 5px solid #3498db;
        }

        .section h2 {
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section h3 {
            color: #34495e;
            font-size: 1.3em;
            margin: 20px 0 10px 0;
        }

        .summary {
            font-size: 1.1em;
            line-height: 1.8;
            color: #555;
        }

        .key-points, .action-items, .decisions {
            list-style: none;
        }

        .key-points li, .action-items li, .decisions li {
            padding: 10px 0;
            padding-left: 30px;
            position: relative;
            border-bottom: 1px solid #eee;
        }

        .key-points li:before {
            content: "🎯";
            position: absolute;
            left: 0;
        }

        .action-items li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }

        .decisions li:before {
            content: "🔹";
            position: absolute;
            left: 0;
        }

        .timeline-item {
            background: white;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-left: 4px solid #e74c3c;
        }

        .timestamp {
            font-weight: bold;
            color: #3498db;
            font-size: 1.1em;
        }

        .transcription-segment {
            background: white;
            padding: 15px 20px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 3px solid #27ae60;
        }

        .confidence-high { border-left-color: #27ae60; }
        .confidence-medium { border-left-color: #f39c12; }
        .confidence-low { border-left-color: #e74c3c; }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .footer {
            text-align: center;
            padding: 30px;
            color: #666;
            border-top: 1px solid #eee;
            background: #f8f9fa;
        }

        .frames-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .frame-item {
            background: white;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border: 1px solid #e1e8ed;
        }

        .frame-header {
            margin-bottom: 12px;
        }

        .frame-header .timestamp {
            font-weight: bold;
            color: #2c3e50;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .frame-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .frame-type {
            background: #3498db;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .confidence {
            background: #f8f9fa;
            color: #6c757d;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .frame-image {
            margin-top: 10px;
        }

        .frame-placeholder {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            color: #6c757d;
            margin-top: 10px;
        }

        .transcription-container {
            max-height: 600px;
            overflow-y: auto;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            padding: 15px;
            background: #fafbfc;
        }

        .transcription-segment {
            background: white;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
            border-left: 3px solid #3498db;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .transcription-segment.confidence-high {
            border-left-color: #27ae60;
        }

        .transcription-segment.confidence-medium {
            border-left-color: #f39c12;
        }

        .transcription-segment.confidence-low {
            border-left-color: #e74c3c;
        }

        .segment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .segment-number {
            background: #ecf0f1;
            color: #2c3e50;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 500;
        }

        .confidence-badge {
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 500;
            font-size: 11px;
        }

        .confidence-badge.confidence-high {
            background: #d5f4e6;
            color: #27ae60;
        }

        .confidence-badge.confidence-medium {
            background: #fef9e7;
            color: #f39c12;
        }

        .confidence-badge.confidence-low {
            background: #fadbd8;
            color: #e74c3c;
        }

        .segment-text {
            margin: 0;
            line-height: 1.5;
            color: #2c3e50;
        }

        .timeline-transcription {
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            padding: 20px;
            background: #fafbfc;
        }

        .timeline-frame-marker {
            background: #e8f4fd;
            border: 1px solid #3498db;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #3498db;
        }

        .frame-marker-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .frame-timestamp {
            font-weight: bold;
            color: #2980b9;
            font-size: 14px;
        }

        .frame-type-badge {
            background: #3498db;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .inline-frame-image {
            margin-top: 10px;
        }

        .frame-placeholder-inline {
            background: #f8f9fa;
            border: 1px dashed #dee2e6;
            border-radius: 6px;
            padding: 10px;
            text-align: center;
            color: #6c757d;
            margin-top: 10px;
            font-size: 14px;
        }

        .timeline-transcription-segment {
            background: white;
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            border-left: 3px solid #27ae60;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .timeline-transcription-segment.confidence-high {
            border-left-color: #27ae60;
        }

        .timeline-transcription-segment.confidence-medium {
            border-left-color: #f39c12;
        }

        .timeline-transcription-segment.confidence-low {
            border-left-color: #e74c3c;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .content {
                padding: 20px;
            }

            .section {
                padding: 20px;
            }

            .frames-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Meeting Analysis Report</h1>
            <div class="meta">
                <p><strong>PMI + Attentional window - results of the day  (MEETING RECORDING)_20250618_135323.mp4</strong></p>
                <p>2025-06-22 16:09:25 • 9m 20s • RU</p>
            </div>
        </div>

        <div class="content">
        
            <div class="section">
                <h2>📋 Executive Summary</h2>
                <div class="summary">Совещание было посвящено обсуждению корректировки ставок, использованию GPT для предложений изменений, а также отчету о проделанной работе по автоматизации. В ходе встречи были рассмотрены возможности применения предложений GPT и обсуждены текущие задачи по автоматизации.</div>
            </div>
            
                <div class="section">
                    <h2>🎯 Key Discussion Points</h2>
                    <ul class="key-points">
                <li>Обсуждение корректировки ставок с помощью GPT</li><li>Возможность прислушаться к предложениям GPT или настаивать на собственных ставках</li><li>Отчет о проделанной работе по автоматизации и использовании Jira для декомпозиции задач</li>
                    </ul>
                </div>
                
                <div class="section">
                    <h2>✅ Action Items</h2>
                    <ul class="action-items">
                <li>София должна обсудить предложенный GPT вариант и приложить его к текущему событию</li><li>Продолжить работу по автоматизации типизированных интеграций</li>
                    </ul>
                </div>
                
            <div class="section">
                <h2>🖼️ Visual Timeline</h2>
                <div class="frames-grid">
            
                <div class="frame-item">
                    <div class="frame-header">
                        <div class="timestamp">Frame 1 - 02:12</div>
                        <div class="frame-meta">
                            <span class="frame-type">Speaker</span>
                            <span class="confidence">Score: 1.00</span>
                        </div>
                    </div>
                
                    <div class="frame-placeholder">
                        <p>📷 Frame not available</p>
                        <p><small>scene_002_frame_00_000132.jpg</small></p>
                    </div>
                    </div>
                <div class="frame-item">
                    <div class="frame-header">
                        <div class="timestamp">Frame 2 - 02:14</div>
                        <div class="frame-meta">
                            <span class="frame-type">Speaker</span>
                            <span class="confidence">Score: 0.17</span>
                        </div>
                    </div>
                
                    <div class="frame-placeholder">
                        <p>📷 Frame not available</p>
                        <p><small>scene_003_frame_00_000134.jpg</small></p>
                    </div>
                    </div>
                <div class="frame-item">
                    <div class="frame-header">
                        <div class="timestamp">Frame 3 - 02:18</div>
                        <div class="frame-meta">
                            <span class="frame-type">Speaker</span>
                            <span class="confidence">Score: 0.22</span>
                        </div>
                    </div>
                
                    <div class="frame-placeholder">
                        <p>📷 Frame not available</p>
                        <p><small>scene_004_frame_00_000138.jpg</small></p>
                    </div>
                    </div>
                <div class="frame-item">
                    <div class="frame-header">
                        <div class="timestamp">Frame 4 - 03:27</div>
                        <div class="frame-meta">
                            <span class="frame-type">Speaker</span>
                            <span class="confidence">Score: 0.58</span>
                        </div>
                    </div>
                
                    <div class="frame-placeholder">
                        <p>📷 Frame not available</p>
                        <p><small>scene_009_frame_00_000207.jpg</small></p>
                    </div>
                    </div>
                <div class="frame-item">
                    <div class="frame-header">
                        <div class="timestamp">Frame 5 - 04:31</div>
                        <div class="frame-meta">
                            <span class="frame-type">Speaker</span>
                            <span class="confidence">Score: 0.00</span>
                        </div>
                    </div>
                
                    <div class="frame-placeholder">
                        <p>📷 Frame not available</p>
                        <p><small>scene_009_frame_01_000271.jpg</small></p>
                    </div>
                    </div>
                <div class="frame-item">
                    <div class="frame-header">
                        <div class="timestamp">Frame 6 - 05:35</div>
                        <div class="frame-meta">
                            <span class="frame-type">Speaker</span>
                            <span class="confidence">Score: 0.01</span>
                        </div>
                    </div>
                
                    <div class="frame-placeholder">
                        <p>📷 Frame not available</p>
                        <p><small>scene_009_frame_02_000335.jpg</small></p>
                    </div>
                    </div>
                <div class="frame-item">
                    <div class="frame-header">
                        <div class="timestamp">Frame 7 - 07:02</div>
                        <div class="frame-meta">
                            <span class="frame-type">Speaker</span>
                            <span class="confidence">Score: 0.57</span>
                        </div>
                    </div>
                
                    <div class="frame-placeholder">
                        <p>📷 Frame not available</p>
                        <p><small>scene_010_frame_00_000422.jpg</small></p>
                    </div>
                    </div>
                <div class="frame-item">
                    <div class="frame-header">
                        <div class="timestamp">Frame 8 - 07:25</div>
                        <div class="frame-meta">
                            <span class="frame-type">Speaker</span>
                            <span class="confidence">Score: 0.33</span>
                        </div>
                    </div>
                
                    <div class="frame-placeholder">
                        <p>📷 Frame not available</p>
                        <p><small>scene_010_frame_01_000445.jpg</small></p>
                    </div>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>📝 Frame-Synchronized Transcription</h2>
                <p style="margin-bottom: 20px;">Complete meeting transcript synchronized with visual timeline (13 segments, 9 frames):</p>
                <div class="timeline-transcription">
            
                        <div class="timeline-transcription-segment confidence-high">
                            <div class="segment-header">
                                <span class="timestamp">🎤 [00:00 - 00:01]</span>
                                <span class="confidence-badge confidence-high">100%</span>
                            </div>
                            <p class="segment-text">Спасибо.</p>
                        </div>
                        
                    <div class="timeline-frame-marker">
                        <div class="frame-marker-header">
                            <span class="frame-timestamp">🖼️ Frame at 02:12</span>
                            <span class="frame-type-badge">Speaker</span>
                        </div>
                    
                        <div class="frame-placeholder-inline">
                            <span>📷 scene_002_frame_00_000132.jpg</span>
                        </div>
                        </div>
                    <div class="timeline-frame-marker">
                        <div class="frame-marker-header">
                            <span class="frame-timestamp">🖼️ Frame at 02:14</span>
                            <span class="frame-type-badge">Speaker</span>
                        </div>
                    
                        <div class="frame-placeholder-inline">
                            <span>📷 scene_003_frame_00_000134.jpg</span>
                        </div>
                        </div>
                    <div class="timeline-frame-marker">
                        <div class="frame-marker-header">
                            <span class="frame-timestamp">🖼️ Frame at 02:18</span>
                            <span class="frame-type-badge">Speaker</span>
                        </div>
                    
                        <div class="frame-placeholder-inline">
                            <span>📷 scene_004_frame_00_000138.jpg</span>
                        </div>
                        </div>
                    <div class="timeline-frame-marker">
                        <div class="frame-marker-header">
                            <span class="frame-timestamp">🖼️ Frame at 03:27</span>
                            <span class="frame-type-badge">Speaker</span>
                        </div>
                    
                        <div class="frame-placeholder-inline">
                            <span>📷 scene_009_frame_00_000207.jpg</span>
                        </div>
                        </div>
                        <div class="timeline-transcription-segment confidence-high">
                            <div class="segment-header">
                                <span class="timestamp">🎤 [03:30 - 03:35]</span>
                                <span class="confidence-badge confidence-high">100%</span>
                            </div>
                            <p class="segment-text">GPT предложил следующие ставки сократить до 30-20-10.</p>
                        </div>
                        
                        <div class="timeline-transcription-segment confidence-high">
                            <div class="segment-header">
                                <span class="timestamp">🎤 [03:36 - 03:39]</span>
                                <span class="confidence-badge confidence-high">100%</span>
                            </div>
                            <p class="segment-text">У нас experience был с партнерами,</p>
                        </div>
                        
                        <div class="timeline-transcription-segment confidence-high">
                            <div class="segment-header">
                                <span class="timestamp">🎤 [03:40 - 03:43]</span>
                                <span class="confidence-badge confidence-high">100%</span>
                            </div>
                            <p class="segment-text">кто перепродает лицензию, professional services и так далее.</p>
                        </div>
                        
                        <div class="timeline-transcription-segment confidence-high">
                            <div class="segment-header">
                                <span class="timestamp">🎤 [03:44 - 03:46]</span>
                                <span class="confidence-badge confidence-high">100%</span>
                            </div>
                            <p class="segment-text">Например, в части лицензии это в зависимости от оборота.</p>
                        </div>
                        
                        <div class="timeline-transcription-segment confidence-high">
                            <div class="segment-header">
                                <span class="timestamp">🎤 [03:47 - 03:49]</span>
                                <span class="confidence-badge confidence-high">100%</span>
                            </div>
                            <p class="segment-text">То есть если они продали лицензию за 30 долларов,</p>
                        </div>
                        
                        <div class="timeline-transcription-segment confidence-high">
                            <div class="segment-header">
                                <span class="timestamp">🎤 [03:49 - 03:50]</span>
                                <span class="confidence-badge confidence-high">100%</span>
                            </div>
                            <p class="segment-text">ну какие 50%?</p>
                        </div>
                        
                        <div class="timeline-transcription-segment confidence-high">
                            <div class="segment-header">
                                <span class="timestamp">🎤 [03:54 - 03:57]</span>
                                <span class="confidence-badge confidence-high">100%</span>
                            </div>
                            <p class="segment-text">Ну вот поэтому мы тут можем настаивать на своих ставках</p>
                        </div>
                        
                        <div class="timeline-transcription-segment confidence-high">
                            <div class="segment-header">
                                <span class="timestamp">🎤 [03:57 - 03:59]</span>
                                <span class="confidence-badge confidence-high">100%</span>
                            </div>
                            <p class="segment-text">или прислушаться к GPT.</p>
                        </div>
                        
                    <div class="timeline-frame-marker">
                        <div class="frame-marker-header">
                            <span class="frame-timestamp">🖼️ Frame at 04:31</span>
                            <span class="frame-type-badge">Speaker</span>
                        </div>
                    
                        <div class="frame-placeholder-inline">
                            <span>📷 scene_009_frame_01_000271.jpg</span>
                        </div>
                        </div>
                    <div class="timeline-frame-marker">
                        <div class="frame-marker-header">
                            <span class="frame-timestamp">🖼️ Frame at 05:35</span>
                            <span class="frame-type-badge">Speaker</span>
                        </div>
                    
                        <div class="frame-placeholder-inline">
                            <span>📷 scene_009_frame_02_000335.jpg</span>
                        </div>
                        </div>
                        <div class="timeline-transcription-segment confidence-high">
                            <div class="segment-header">
                                <span class="timestamp">🎤 [07:00 - 07:08]</span>
                                <span class="confidence-badge confidence-high">100%</span>
                            </div>
                            <p class="segment-text">Ну да, но я имею в виду, что, понятно, их можно корректировать, но для шаблона София тоже посмотри, попробуй GPT такой вариант обсудить.</p>
                        </div>
                        
                    <div class="timeline-frame-marker">
                        <div class="frame-marker-header">
                            <span class="frame-timestamp">🖼️ Frame at 07:02</span>
                            <span class="frame-type-badge">Speaker</span>
                        </div>
                    
                        <div class="frame-placeholder-inline">
                            <span>📷 scene_010_frame_00_000422.jpg</span>
                        </div>
                        </div>
                        <div class="timeline-transcription-segment confidence-high">
                            <div class="segment-header">
                                <span class="timestamp">🎤 [07:10 - 07:15]</span>
                                <span class="confidence-badge confidence-high">100%</span>
                            </div>
                            <p class="segment-text">Хорошо, я это приложу тогда к нашему событию, которое уже существует, мы начинали обсуждение.</p>
                        </div>
                        
                        <div class="timeline-transcription-segment confidence-high">
                            <div class="segment-header">
                                <span class="timestamp">🎤 [07:17 - 07:21]</span>
                                <span class="confidence-badge confidence-high">100%</span>
                            </div>
                            <p class="segment-text">И докладываю еще по автоматизации кратко, что было сделано.</p>
                        </div>
                        
                        <div class="timeline-transcription-segment confidence-high">
                            <div class="segment-header">
                                <span class="timestamp">🎤 [07:22 - 07:29]</span>
                                <span class="confidence-badge confidence-high">100%</span>
                            </div>
                            <p class="segment-text">Мы написали план, построили граф, декомпозировали задачи в Jira, ряд задач по автоматизации работы с типизованными интеграциями был реализован.</p>
                        </div>
                        
                    <div class="timeline-frame-marker">
                        <div class="frame-marker-header">
                            <span class="frame-timestamp">🖼️ Frame at 07:25</span>
                            <span class="frame-type-badge">Speaker</span>
                        </div>
                    
                        <div class="frame-placeholder-inline">
                            <span>📷 scene_010_frame_01_000445.jpg</span>
                        </div>
                        </div>
                    <div class="timeline-frame-marker">
                        <div class="frame-marker-header">
                            <span class="frame-timestamp">🖼️ Frame at 07:48</span>
                            <span class="frame-type-badge">Speaker</span>
                        </div>
                    
                        <div class="frame-placeholder-inline">
                            <span>📷 scene_010_frame_02_000468.jpg</span>
                        </div>
                        </div>
                </div>
            </div>
            
        <div class="section">
            <h2>📊 Meeting Statistics</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">9m 20s</div>
                    <div class="stat-label">Duration</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">122</div>
                    <div class="stat-label">Total Words</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">13</div>
                    <div class="stat-label">Words/Minute</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">13</div>
                    <div class="stat-label">Segments</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">9</div>
                    <div class="stat-label">Key Frames</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">Avg Confidence</div>
                </div>
            </div>
        </div>
        
        </div>

        <div class="footer">
            <p>Report generated on 2025-06-22 19:31:49 by Meeting Transcription & Analysis API</p>
        </div>
    </div>
</body>
</html>
        